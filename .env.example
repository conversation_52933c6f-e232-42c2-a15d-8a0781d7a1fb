# 狼人杀AI游戏环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# ==================== 基础配置 ====================

# 运行环境 (development, testing, production)
WOLFKILL_ENV=development
NODE_ENV=development
FLASK_ENV=development

# ==================== 服务端口配置 ====================

# 后端服务端口
BACKEND_PORT=8000

# 前端服务端口
FRONTEND_PORT=3000

# 数据库端口
POSTGRES_PORT=5432
REDIS_PORT=6379

# Nginx端口
HTTP_PORT=80
HTTPS_PORT=443

# 监控服务端口
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# ==================== 前端配置 ====================

# React应用API地址
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WEBSOCKET_URL=http://localhost:8000
REACT_APP_VERSION=1.0.0
REACT_APP_DEBUG=true

# 构建配置
GENERATE_SOURCEMAP=true
BROWSER=chrome

# ==================== 后端配置 ====================

# Flask配置
FLASK_DEBUG=1
SECRET_KEY=your-secret-key-change-in-production

# 数据库配置
DATABASE_URL=postgresql://wolfkill_user:secure_password@localhost:5432/wolfkill
# 或使用SQLite (开发环境)
# DATABASE_URL=sqlite:///wolfkill.db

# Redis配置
REDIS_URL=redis://localhost:6379

# ==================== 数据库配置 ====================

# PostgreSQL配置
POSTGRES_DB=wolfkill
POSTGRES_USER=wolfkill_user
POSTGRES_PASSWORD=secure_password

# ==================== LLM API配置 ====================

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic API配置
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 本地LLM配置
QWEN3_BASE_URL=http://localhost:8005/v1
QWEN3_API_KEY=EMPTY

# ==================== 安全配置 ====================

# JWT密钥
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# 会话配置
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# ==================== 监控配置 ====================

# Grafana配置
GRAFANA_PASSWORD=admin

# 日志级别
LOG_LEVEL=INFO

# ==================== 游戏配置覆盖 ====================

# AI配置覆盖
# WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ENABLED=true
# WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ADAPTATION_RATE=0.2
# WOLFKILL_AI_CONFIG_DIFFICULTY_LEVELS_NORMAL_PARAMETERS_DECISION_ACCURACY=0.8

# 游戏配置覆盖
# WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_GAME_MODE=balanced
# WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_AUTO_START=true
# WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_TIME_SETTINGS_DAY_PHASE_TIME=240

# LLM配置覆盖
# WOLFKILL_LLM_CONFIG_DEFAULT_PROVIDER=openai_gpt4
# WOLFKILL_LLM_CONFIG_PROVIDERS_QWEN3_30B_BASE_URL=http://localhost:8005/v1

# 服务配置覆盖
# WOLFKILL_SERVICES_CONFIG_BACKEND_SERVER_PORT=8001
# WOLFKILL_SERVICES_CONFIG_FRONTEND_DEV_SERVER_PORT=3001

# ==================== 开发配置 ====================

# 调试配置
DEBUG=true
VERBOSE_LOGGING=true

# 热重载配置
HOT_RELOAD=true
WATCH_FILES=true

# 测试配置
TESTING=false
TEST_DATABASE_URL=sqlite:///test_wolfkill.db

# ==================== 生产配置 ====================

# 性能配置
# WORKERS=4
# MAX_CONNECTIONS=1000
# KEEPALIVE_TIMEOUT=5

# 缓存配置
# CACHE_TYPE=redis
# CACHE_DEFAULT_TIMEOUT=300

# SSL配置
# SSL_CERT_PATH=/etc/ssl/certs/wolfkill.crt
# SSL_KEY_PATH=/etc/ssl/private/wolfkill.key

# 备份配置
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
