# 狼人杀AI游戏环境变量配置（支持变量替换）
# 复制此文件为 .env 并根据需要修改配置

# ==================== 基础配置 ====================

# 运行环境
WOLFKILL_ENV=development
NODE_ENV=development
FLASK_ENV=development

# ==================== 服务端口配置 ====================

# 后端服务端口
BACKEND_PORT=8000

# 前端服务端口
FRONTEND_PORT=3000

# ==================== 自动配置（支持变量替换）====================

# 前端API连接地址（自动使用后端端口）
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}

# 其他配置也可以使用变量
DATABASE_URL=postgresql://wolfkill_user:secure_password@localhost:${POSTGRES_PORT:-5432}/wolfkill

# ==================== LLM API配置 ====================

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地LLM配置
QWEN3_BASE_URL=http://localhost:8005/v1
QWEN3_API_KEY=EMPTY

# ==================== 其他配置 ====================

# 数据库端口（带默认值）
POSTGRES_PORT=5432
REDIS_PORT=6379

# 监控端口
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# 调试配置
DEBUG=true
VERBOSE_LOGGING=true
