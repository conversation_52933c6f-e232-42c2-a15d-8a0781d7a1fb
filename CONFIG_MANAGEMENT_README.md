# 配置管理系统使用指南

## 概述

狼人杀AI游戏现在使用统一的配置管理系统来管理所有超参数和设置。这个系统提供了以下特性：

- 📁 **统一配置文件**: 所有配置集中在YAML文件中
- 🔄 **热重载**: 配置文件修改后自动重新加载
- 🌍 **环境变量覆盖**: 支持通过环境变量覆盖配置
- ✅ **配置验证**: 自动验证配置文件的正确性
- 🛠️ **CLI工具**: 提供命令行工具管理配置

## 配置文件结构

```
configs/
├── ai_config.yaml      # AI超参数配置
├── game_config.yaml    # 游戏规则配置
└── llm_config.yaml     # 大语言模型配置
```

## 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 迁移现有配置（如果需要）

```bash
python tools/config_migration.py
```

### 3. 验证配置

```bash
python tools/config_cli.py validate --all
```

### 4. 查看配置

```bash
python tools/config_cli.py list
python tools/config_cli.py show ai_config
```

## 配置文件详解

### AI配置 (ai_config.yaml)

包含AI相关的所有超参数：

- **难度等级**: 6个难度等级的参数设置
- **个性类型**: 8种个性类型的特征定义
- **角色策略**: 各角色专用的AI策略参数
- **学习设置**: AI学习和适应相关配置
- **行为设置**: AI行为调整参数
- **团队设置**: AI团队配置和推荐组合

### 游戏配置 (game_config.yaml)

包含游戏规则和模式配置：

- **游戏模式**: 经典、简单、复杂、平衡、快速模式
- **默认设置**: 游戏的默认参数
- **胜利条件**: 各阵营的胜利条件
- **平衡设置**: 游戏平衡相关配置
- **游戏流程**: 各阶段的时间和规则设置

### LLM配置 (llm_config.yaml)

包含大语言模型相关配置：

- **提供者配置**: 各种LLM提供者的设置
- **提示词模板**: 角色专用和通用提示词
- **使用策略**: 负载均衡和成本优化策略
- **质量控制**: 响应验证和内容过滤
- **监控配置**: 性能监控和日志设置

## 使用方法

### 在代码中使用配置

```python
from src.config.config_manager import get_config, set_config

# 获取配置值
difficulty_params = get_config("ai_config", "difficulty_levels.normal.parameters")
game_mode = get_config("game_config", "default_settings.game_mode")
llm_provider = get_config("llm_config", "default_provider")

# 获取嵌套配置
suspicion_threshold = get_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold")

# 设置配置值（运行时修改）
set_config("ai_config", "learning_settings.enabled", True, save=False)

# 获取配置时提供默认值
unknown_value = get_config("ai_config", "unknown_key", default="默认值")
```

### 环境变量覆盖

可以通过环境变量覆盖配置文件中的值：

```bash
# 启用AI学习功能
export WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ENABLED=true

# 修改适应速率
export WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ADAPTATION_RATE=0.2

# 修改默认游戏模式
export WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_GAME_MODE=balanced

# 修改LLM提供者
export WOLFKILL_LLM_CONFIG_DEFAULT_PROVIDER=openai_gpt4
```

环境变量命名规则：`WOLFKILL_<配置名>_<键路径>`

## CLI工具使用

### 配置验证

```bash
# 验证所有配置
python tools/config_cli.py validate --all

# 验证特定配置
python tools/config_cli.py validate ai_config

# 验证指定文件
python tools/config_cli.py validate ai_config --file configs/ai_config.yaml
```

### 配置查看

```bash
# 列出所有配置
python tools/config_cli.py list

# 显示完整配置
python tools/config_cli.py show ai_config

# 显示特定配置项
python tools/config_cli.py show ai_config --key difficulty_levels.normal

# 显示配置信息
python tools/config_cli.py info ai_config
```

### 配置修改

```bash
# 设置配置值
python tools/config_cli.py set ai_config learning_settings.enabled true

# 设置但不保存到文件
python tools/config_cli.py set ai_config learning_settings.enabled true --no-save
```

### 配置导入导出

```bash
# 导出配置
python tools/config_cli.py export ai_config ai_config_backup.yaml

# 导入配置
python tools/config_cli.py import ai_config ai_config_backup.yaml

# 创建默认配置
python tools/config_cli.py default ai_config --output default_ai_config.yaml
```

### 配置监控

```bash
# 监控配置文件变化（热重载）
python tools/config_cli.py watch

# 性能基准测试
python tools/config_cli.py benchmark
```

## 配置验证工具

独立的配置验证工具提供更详细的验证信息：

```bash
# 验证配置文件
python tools/config_validator.py configs/ai_config.yaml

# 验证多个文件
python tools/config_validator.py configs/*.yaml

# 生成验证报告
python tools/config_validator.py configs/*.yaml --report validation_report.txt

# 严格模式（警告也视为错误）
python tools/config_validator.py configs/*.yaml --strict
```

## 最佳实践

### 1. 配置文件管理

- 将配置文件纳入版本控制
- 为不同环境创建不同的配置文件
- 使用环境变量覆盖敏感信息（如API密钥）

### 2. 开发流程

- 修改配置后运行验证工具
- 使用热重载功能进行开发调试
- 定期备份重要配置

### 3. 部署建议

- 在生产环境中禁用热重载
- 使用环境变量管理环境特定配置
- 设置配置文件的适当权限

## 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   python tools/config_migration.py
   ```

2. **配置验证失败**
   ```bash
   python tools/config_validator.py configs/ai_config.yaml
   ```

3. **环境变量不生效**
   - 检查环境变量名称格式
   - 确保重新加载了配置

4. **热重载不工作**
   - 检查文件权限
   - 确保启用了热重载功能

### 调试技巧

- 使用 `config_cli.py info` 查看配置状态
- 检查 `migration_report.txt` 了解迁移详情
- 使用 `config_validator.py` 获取详细错误信息

## 示例代码

查看 `examples/config_usage_example.py` 了解完整的使用示例。

## 更多信息

- 配置管理器源码: `src/config/config_manager.py`
- CLI工具源码: `tools/config_cli.py`
- 验证工具源码: `tools/config_validator.py`
- 迁移工具源码: `tools/config_migration.py`
