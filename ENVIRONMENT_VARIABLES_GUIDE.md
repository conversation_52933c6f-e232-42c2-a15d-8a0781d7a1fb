# 环境变量配置指南

## 🎯 解决端口一致性问题

现在你可以使用**变量替换**来保持配置一致性，就像你想要的那样！

## 📝 支持的配置方式

### 方式1: 变量替换（推荐）

在 `.env` 文件中使用变量引用：

```bash
# 设置后端端口
BACKEND_PORT=8001

# 前端自动使用相同端口（变量替换）
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}

# 数据库URL也可以使用变量
DATABASE_URL=postgresql://user:pass@localhost:${POSTGRES_PORT}/wolfkill
```

### 方式2: 配置系统自动一致性

只设置后端端口，系统自动生成前端连接地址：

```bash
# 只需要设置这个
WOLFKILL_SERVICES_CONFIG_BACKEND_SERVER_PORT=8001

# 系统会自动设置：
# - 后端监听: 0.0.0.0:8001
# - 前端连接: http://localhost:8001/api
```

### 方式3: 传统方式（手动保持一致）

```bash
BACKEND_PORT=8001
REACT_APP_API_URL=http://localhost:8001/api
REACT_APP_WEBSOCKET_URL=http://localhost:8001
```

## 🚀 使用方法

### 1. 创建环境变量文件

```bash
# 复制模板（已支持变量替换）
cp .env.example .env

# 或使用增强版模板
python scripts/setup_env.py --create-template
cp .env.template.enhanced .env
```

### 2. 编辑配置

```bash
# 编辑 .env 文件
vim .env

# 示例内容：
BACKEND_PORT=8001
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}
```

### 3. 启动服务

#### 自动处理变量替换

```bash
# 前端启动脚本会自动处理变量替换
./start_frontend.sh

# 或使用统一启动脚本
python scripts/start_services.py
```

#### 手动处理变量替换

```bash
# 查看处理后的变量
python scripts/setup_env.py --show-vars

# 应用到当前shell
source <(python scripts/setup_env.py)

# 然后启动服务
npm start
```

## 🔧 变量替换语法

### 支持的格式

```bash
# ${VAR} 格式（推荐）
API_URL=http://localhost:${BACKEND_PORT}/api

# $VAR 格式
API_URL=http://localhost:$BACKEND_PORT/api

# 带默认值（仅 ${VAR:-default} 格式）
DATABASE_PORT=${POSTGRES_PORT:-5432}
```

### 嵌套变量

```bash
# 基础变量
HOST=localhost
PORT=8000

# 组合变量
BASE_URL=http://${HOST}:${PORT}
API_URL=${BASE_URL}/api
WEBSOCKET_URL=${BASE_URL}
```

## 📊 验证配置

### 检查变量替换结果

```bash
# 显示处理后的所有变量
python scripts/setup_env.py --show-vars

# 验证服务配置
python tools/services_cli.py validate

# 查看最终配置
python tools/services_cli.py show
```

### 测试连接

```bash
# 检查后端是否在正确端口启动
curl http://localhost:${BACKEND_PORT}/api/health

# 检查前端配置
echo $REACT_APP_API_URL
```

## 🎯 常见使用场景

### 场景1: 开发环境端口冲突

```bash
# .env 文件
BACKEND_PORT=8001
FRONTEND_PORT=3001

# 自动生成的连接地址
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}
```

### 场景2: 多环境部署

```bash
# 开发环境 (.env.development)
BACKEND_PORT=8000
HOST=localhost

# 生产环境 (.env.production)  
BACKEND_PORT=80
HOST=api.wolfkill.com

# 通用配置
REACT_APP_API_URL=http://${HOST}:${BACKEND_PORT}/api
```

### 场景3: Docker部署

```bash
# Docker环境变量
BACKEND_HOST=backend
BACKEND_PORT=8000

# 容器内连接地址
REACT_APP_API_URL=http://${BACKEND_HOST}:${BACKEND_PORT}/api
```

## ⚠️ 注意事项

### 1. 变量替换限制

- 仅在使用 `scripts/setup_env.py` 或 `start_frontend.sh` 时生效
- 原生 `.env` 文件不支持变量替换
- 需要 Python 3.6+ 支持

### 2. 优先级顺序

```
1. 环境变量脚本处理的变量
2. 配置文件中的设置
3. 系统环境变量
4. 默认值
```

### 3. 调试技巧

```bash
# 查看变量替换过程
python scripts/setup_env.py --show-vars --env-file .env

# 检查最终环境变量
env | grep REACT_APP

# 验证服务连接
python tools/services_cli.py status
```

## 🔄 迁移指南

### 从手动配置迁移

**之前（手动保持一致）：**
```bash
BACKEND_PORT=8000
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WEBSOCKET_URL=http://localhost:8000
```

**现在（自动一致）：**
```bash
BACKEND_PORT=8000
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}
```

### 验证迁移结果

```bash
# 1. 更新 .env 文件使用变量替换
# 2. 测试变量处理
python scripts/setup_env.py --show-vars

# 3. 启动服务验证
./start_frontend.sh
```

## 💡 最佳实践

1. **使用变量替换** - 避免手动维护多个相同的端口号
2. **验证配置** - 启动前检查变量替换结果
3. **环境隔离** - 不同环境使用不同的 `.env` 文件
4. **敏感信息保护** - API密钥等使用环境变量，不写入配置文件

现在你可以像你想要的那样使用 `${BACKEND_PORT}` 来保持配置一致性了！🎉
