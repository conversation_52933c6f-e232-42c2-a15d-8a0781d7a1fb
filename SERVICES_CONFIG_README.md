# 服务配置管理指南

## 概述

狼人杀AI游戏现在使用统一的服务配置管理系统，支持前端和后端服务的配置管理、环境变量覆盖、容器化部署等功能。

## 配置文件结构

```
configs/
└── services_config.yaml    # 服务配置文件

docker/
├── Dockerfile.backend      # 后端Docker文件
├── Dockerfile.frontend     # 前端Docker文件
├── nginx.conf              # Nginx主配置
└── nginx-frontend.conf     # 前端Nginx配置

scripts/
└── start_services.py       # 服务启动脚本

tools/
└── services_cli.py         # 服务管理CLI工具

.env.example                 # 环境变量模板
docker-compose.yml          # Docker Compose配置
```

## 快速开始

### 1. 环境准备

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 2. 验证配置

```bash
# 验证服务配置
python tools/services_cli.py validate

# 查看配置信息
python tools/services_cli.py show
```

### 3. 启动服务

#### 方式一：使用统一启动脚本

```bash
# 启动所有服务
python scripts/start_services.py

# 启动指定服务
python scripts/start_services.py --service backend
python scripts/start_services.py --service frontend

# 指定环境
python scripts/start_services.py --env production
```

#### 方式二：使用传统启动脚本

```bash
# 启动后端
python start_backend.py

# 启动前端
./start_frontend.sh
```

#### 方式三：使用Docker

```bash
# 启动所有服务
docker-compose up -d

# 启动指定服务
docker-compose up -d backend frontend

# 查看日志
docker-compose logs -f
```

## 配置详解

### 服务配置 (services_config.yaml)

#### 后端配置

```yaml
backend:
  server:
    host: "0.0.0.0"
    port: 8000
    debug: true
  
  cors:
    origins:
      - "http://localhost:3000"
    methods: ["GET", "POST", "PUT", "DELETE"]
  
  websocket:
    cors_allowed_origins:
      - "http://localhost:3000"
    async_mode: "threading"
```

#### 前端配置

```yaml
frontend:
  dev_server:
    port: 3000
    host: "localhost"
    hot_reload: true
  
  api_connection:
    base_url: "http://localhost:8000/api"
    websocket_url: "http://localhost:8000"
    timeout: 10000
```

#### 环境配置

```yaml
development:
  startup_order:
    - "backend"
    - "frontend"
  
  health_checks:
    backend:
      url: "http://localhost:8000/api/health"
      interval: 30
```

### 环境变量

#### 基础环境变量

```bash
# 运行环境
WOLFKILL_ENV=development
NODE_ENV=development
FLASK_ENV=development

# 服务端口
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

#### 配置覆盖

```bash
# 覆盖后端端口
WOLFKILL_SERVICES_CONFIG_BACKEND_SERVER_PORT=8001

# 覆盖前端端口
WOLFKILL_SERVICES_CONFIG_FRONTEND_DEV_SERVER_PORT=3001

# 覆盖API地址
WOLFKILL_SERVICES_CONFIG_FRONTEND_API_CONNECTION_BASE_URL=http://localhost:8001/api
```

## CLI工具使用

### 服务管理CLI

```bash
# 验证配置
python tools/services_cli.py validate

# 显示配置
python tools/services_cli.py show
python tools/services_cli.py show --service backend

# 检查服务状态
python tools/services_cli.py status

# 启动服务
python tools/services_cli.py start backend
python tools/services_cli.py start frontend

# 停止服务
python tools/services_cli.py stop

# 显示环境变量
python tools/services_cli.py env backend
python tools/services_cli.py env frontend

# 导出配置
python tools/services_cli.py export config.json
```

### 配置管理CLI

```bash
# 验证所有配置
python tools/config_cli.py validate --all

# 显示服务配置
python tools/config_cli.py show services_config

# 设置配置值
python tools/config_cli.py set services_config backend.server.port 8001
```

## Docker部署

### 基础部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 生产部署

```bash
# 使用生产环境配置
WOLFKILL_ENV=production docker-compose up -d

# 启用监控服务
docker-compose --profile monitoring up -d

# 扩展后端服务
docker-compose up -d --scale backend=3
```

### 服务管理

```bash
# 重启服务
docker-compose restart backend

# 更新服务
docker-compose pull
docker-compose up -d

# 备份数据
docker-compose exec postgres pg_dump -U wolfkill_user wolfkill > backup.sql
```

## 环境配置

### 开发环境

```yaml
development:
  backend:
    debug: true
    hot_reload: true
  
  frontend:
    hot_reload: true
    source_maps: true
  
  debugging:
    verbose_logging: true
    error_overlay: true
```

### 生产环境

```yaml
production:
  backend:
    debug: false
    workers: 4
    
  frontend:
    build_optimization: true
    minification: true
    source_maps: false
  
  security:
    csrf_enabled: true
    secure_cookies: true
    https_only: true
```

### 测试环境

```yaml
testing:
  backend:
    port: 8001
    database_url: "sqlite:///test_wolfkill.db"
  
  frontend:
    port: 3001
    api_url: "http://localhost:8001/api"
```

## 监控和日志

### 健康检查

```bash
# 检查后端健康状态
curl http://localhost:8000/api/health

# 检查前端状态
curl http://localhost:3000

# 使用CLI检查
python tools/services_cli.py status
```

### 日志配置

```yaml
backend:
  logging:
    level: "INFO"
    file_enabled: true
    file_path: "logs/backend.log"
    max_bytes: 10485760
    backup_count: 5
```

### 监控服务

```bash
# 启用Prometheus和Grafana
docker-compose --profile monitoring up -d

# 访问监控界面
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3001 (admin/admin)
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8000
   
   # 修改端口配置
   export BACKEND_PORT=8001
   ```

2. **配置文件错误**
   ```bash
   # 验证配置
   python tools/services_cli.py validate
   
   # 查看详细错误
   python tools/config_validator.py configs/services_config.yaml
   ```

3. **服务无法启动**
   ```bash
   # 检查依赖
   python tools/services_cli.py validate
   
   # 查看日志
   docker-compose logs backend
   ```

4. **前后端连接失败**
   ```bash
   # 检查API连接
   curl http://localhost:8000/api/health
   
   # 检查CORS配置
   python tools/services_cli.py show --service backend
   ```

### 调试技巧

- 使用 `--env development` 启用详细日志
- 检查 `logs/` 目录下的日志文件
- 使用 `docker-compose logs -f` 查看实时日志
- 验证环境变量设置：`python tools/services_cli.py env backend`

## 最佳实践

### 开发流程

1. 修改配置后验证：`python tools/services_cli.py validate`
2. 使用环境变量覆盖测试配置
3. 提交前检查所有环境的配置

### 部署建议

1. 生产环境使用Docker部署
2. 配置文件纳入版本控制
3. 敏感信息使用环境变量
4. 定期备份配置和数据

### 安全考虑

1. 更改默认密钥和密码
2. 启用HTTPS（生产环境）
3. 配置防火墙规则
4. 定期更新依赖

## 更多信息

- 服务配置管理器：`src/config/services_config.py`
- 服务启动脚本：`scripts/start_services.py`
- CLI工具：`tools/services_cli.py`
- Docker配置：`docker/`
