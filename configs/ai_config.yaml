# AI超参数配置文件
# 包含AI难度等级、个性参数、策略参数等所有AI相关的超参数

# AI难度等级配置
difficulty_levels:
  beginner:
    name: "新手"
    description: "简单策略，较多随机性，适合新手玩家"
    parameters:
      decision_accuracy: 0.3      # 决策准确性
      strategic_thinking: 0.2     # 策略思维能力
      memory_retention: 0.4       # 记忆保持能力
      pattern_recognition: 0.2    # 模式识别能力
      random_factor: 0.5          # 随机因子
      reaction_time: 0.3          # 反应时间
      information_processing: 0.3 # 信息处理能力
      long_term_planning: 0.2     # 长期规划能力
    
  easy:
    name: "简单"
    description: "基础策略，一些随机性"
    parameters:
      decision_accuracy: 0.5
      strategic_thinking: 0.4
      memory_retention: 0.6
      pattern_recognition: 0.4
      random_factor: 0.3
      reaction_time: 0.5
      information_processing: 0.5
      long_term_planning: 0.4
    
  normal:
    name: "普通"
    description: "平衡策略，适度随机性"
    parameters:
      decision_accuracy: 0.7
      strategic_thinking: 0.6
      memory_retention: 0.7
      pattern_recognition: 0.6
      random_factor: 0.2
      reaction_time: 0.7
      information_processing: 0.7
      long_term_planning: 0.6
    
  hard:
    name: "困难"
    description: "高级策略，少量随机性"
    parameters:
      decision_accuracy: 0.85
      strategic_thinking: 0.8
      memory_retention: 0.85
      pattern_recognition: 0.8
      random_factor: 0.1
      reaction_time: 0.85
      information_processing: 0.85
      long_term_planning: 0.8
    
  expert:
    name: "专家"
    description: "最优策略，极少随机性"
    parameters:
      decision_accuracy: 0.9
      strategic_thinking: 0.9
      memory_retention: 0.9
      pattern_recognition: 0.9
      random_factor: 0.05
      reaction_time: 0.9
      information_processing: 0.9
      long_term_planning: 0.9
    
  master:
    name: "大师"
    description: "完美策略，几乎无随机性"
    parameters:
      decision_accuracy: 0.95
      strategic_thinking: 0.95
      memory_retention: 0.98
      pattern_recognition: 0.95
      random_factor: 0.02
      reaction_time: 0.95
      information_processing: 0.95
      long_term_planning: 0.95

# AI个性类型配置
personality_types:
  aggressive:
    name: "攻击型"
    description: "积极主动，喜欢发起攻击"
    traits:
      aggressiveness: 0.9
      cautiousness: 0.2
      analytical_thinking: 0.6
      social_skills: 0.7
      deception_ability: 0.8
      leadership: 0.8
      adaptability: 0.6
      patience: 0.3
      confidence: 0.9
      cooperation: 0.4
      risk_tolerance: 0.8
      emotional_stability: 0.6
      creativity: 0.7
      intuition: 0.5
      logical_reasoning: 0.6
      empathy: 0.3
    
  defensive:
    name: "防守型"
    description: "谨慎保守，注重防御"
    traits:
      aggressiveness: 0.2
      cautiousness: 0.9
      analytical_thinking: 0.8
      social_skills: 0.5
      deception_ability: 0.3
      leadership: 0.4
      adaptability: 0.5
      patience: 0.9
      confidence: 0.6
      cooperation: 0.8
      risk_tolerance: 0.2
      emotional_stability: 0.9
      creativity: 0.4
      intuition: 0.6
      logical_reasoning: 0.8
      empathy: 0.7
    
  analytical:
    name: "分析型"
    description: "理性分析，逻辑思维强"
    traits:
      aggressiveness: 0.5
      cautiousness: 0.7
      analytical_thinking: 0.95
      social_skills: 0.6
      deception_ability: 0.5
      leadership: 0.7
      adaptability: 0.7
      patience: 0.8
      confidence: 0.7
      cooperation: 0.7
      risk_tolerance: 0.4
      emotional_stability: 0.8
      creativity: 0.6
      intuition: 0.3
      logical_reasoning: 0.95
      empathy: 0.5
    
  intuitive:
    name: "直觉型"
    description: "依靠直觉，反应敏锐"
    traits:
      aggressiveness: 0.6
      cautiousness: 0.4
      analytical_thinking: 0.4
      social_skills: 0.8
      deception_ability: 0.7
      leadership: 0.6
      adaptability: 0.9
      patience: 0.4
      confidence: 0.8
      cooperation: 0.6
      risk_tolerance: 0.7
      emotional_stability: 0.6
      creativity: 0.9
      intuition: 0.95
      logical_reasoning: 0.4
      empathy: 0.8
    
  social:
    name: "社交型"
    description: "善于交际，团队合作"
    traits:
      aggressiveness: 0.4
      cautiousness: 0.5
      analytical_thinking: 0.6
      social_skills: 0.95
      deception_ability: 0.6
      leadership: 0.8
      adaptability: 0.8
      patience: 0.7
      confidence: 0.8
      cooperation: 0.95
      risk_tolerance: 0.5
      emotional_stability: 0.7
      creativity: 0.7
      intuition: 0.7
      logical_reasoning: 0.6
      empathy: 0.9
    
  deceptive:
    name: "欺骗型"
    description: "善于伪装，欺骗能力强"
    traits:
      aggressiveness: 0.7
      cautiousness: 0.6
      analytical_thinking: 0.7
      social_skills: 0.8
      deception_ability: 0.95
      leadership: 0.6
      adaptability: 0.8
      patience: 0.6
      confidence: 0.8
      cooperation: 0.3
      risk_tolerance: 0.7
      emotional_stability: 0.7
      creativity: 0.9
      intuition: 0.8
      logical_reasoning: 0.7
      empathy: 0.2
    
  loyal:
    name: "忠诚型"
    description: "忠诚可靠，团队意识强"
    traits:
      aggressiveness: 0.3
      cautiousness: 0.7
      analytical_thinking: 0.6
      social_skills: 0.7
      deception_ability: 0.2
      leadership: 0.5
      adaptability: 0.5
      patience: 0.8
      confidence: 0.6
      cooperation: 0.95
      risk_tolerance: 0.3
      emotional_stability: 0.9
      creativity: 0.5
      intuition: 0.6
      logical_reasoning: 0.7
      empathy: 0.9
    
  chaotic:
    name: "混乱型"
    description: "不可预测，行为随机"
    traits:
      aggressiveness: 0.6
      cautiousness: 0.3
      analytical_thinking: 0.4
      social_skills: 0.5
      deception_ability: 0.6
      leadership: 0.4
      adaptability: 0.9
      patience: 0.2
      confidence: 0.7
      cooperation: 0.4
      risk_tolerance: 0.9
      emotional_stability: 0.3
      creativity: 0.95
      intuition: 0.8
      logical_reasoning: 0.3
      empathy: 0.5

# 角色专用AI策略参数
role_strategies:
  villager:
    name: "村民AI策略"
    parameters:
      suspicion_threshold: 0.6     # 怀疑阈值
      trust_building: 0.7          # 建立信任能力
      information_sharing: 0.8     # 信息分享倾向
      voting_confidence: 0.6       # 投票信心
      alliance_tendency: 0.7       # 结盟倾向
      leadership_aspiration: 0.5   # 领导欲望
      self_preservation: 0.6       # 自保意识
    difficulty_modifiers:
      easy:
        suspicion_threshold: 0.4
        voting_confidence: 0.4
        information_sharing: 0.5
      hard:
        suspicion_threshold: 0.8
        voting_confidence: 0.9
        information_sharing: 0.9
  
  werewolf:
    name: "狼人AI策略"
    parameters:
      aggression: 0.6              # 攻击性
      deception_skill: 0.7         # 欺骗技巧
      team_coordination: 0.8       # 团队协调
      risk_tolerance: 0.5          # 风险容忍度
      fake_suspicion_rate: 0.3     # 假装怀疑的频率
      target_priority_weight: 0.8  # 目标优先级权重
      self_preservation: 0.7       # 自保意识
    difficulty_modifiers:
      easy:
        deception_skill: 0.4
        team_coordination: 0.5
        fake_suspicion_rate: 0.1
      hard:
        deception_skill: 0.9
        team_coordination: 0.9
        fake_suspicion_rate: 0.5
        risk_tolerance: 0.3

  seer:
    name: "预言家AI策略"
    parameters:
      revelation_threshold: 0.7    # 公开信息的阈值
      leadership_tendency: 0.8     # 领导倾向
      risk_tolerance: 0.6          # 风险容忍度
      trust_building: 0.7          # 建立信任的能力
      strategic_patience: 0.6      # 战略耐心
      information_value: 0.8       # 信息价值评估
      survival_priority: 0.7       # 生存优先级
    difficulty_modifiers:
      easy:
        revelation_threshold: 0.5
        strategic_patience: 0.3
        risk_tolerance: 0.8
      hard:
        revelation_threshold: 0.9
        strategic_patience: 0.9
        risk_tolerance: 0.4
        leadership_tendency: 0.9

  witch:
    name: "女巫AI策略"
    parameters:
      save_threshold: 0.7          # 救人阈值
      poison_threshold: 0.8        # 毒人阈值
      self_preservation: 0.6       # 自保意识
      strategic_patience: 0.7      # 战略耐心
      information_value: 0.8       # 信息价值评估
      potion_conservation: 0.6     # 药剂保存倾向
      revenge_tendency: 0.5        # 复仇倾向
    difficulty_modifiers:
      easy:
        save_threshold: 0.5
        poison_threshold: 0.6
        strategic_patience: 0.4
      hard:
        save_threshold: 0.9
        poison_threshold: 0.9
        strategic_patience: 0.9

  guard:
    name: "守卫AI策略"
    parameters:
      protection_priority: 0.8     # 保护优先级
      target_switching: 0.4        # 目标切换倾向
      self_protection_ban: 0.9     # 自保禁令遵守度
      strategic_thinking: 0.7      # 战略思维
      information_gathering: 0.6   # 信息收集能力
      risk_assessment: 0.8         # 风险评估能力
    difficulty_modifiers:
      easy:
        protection_priority: 0.6
        strategic_thinking: 0.4
        risk_assessment: 0.5
      hard:
        protection_priority: 0.9
        strategic_thinking: 0.9
        risk_assessment: 0.9

  hunter:
    name: "猎人AI策略"
    parameters:
      revenge_accuracy: 0.8        # 复仇准确性
      threat_assessment: 0.7       # 威胁评估能力
      timing_optimization: 0.6     # 时机优化
      information_utilization: 0.8 # 信息利用能力
      strategic_sacrifice: 0.5     # 战略牺牲倾向
      bluff_tendency: 0.4          # 虚张声势倾向
    difficulty_modifiers:
      easy:
        revenge_accuracy: 0.5
        threat_assessment: 0.4
        timing_optimization: 0.3
      hard:
        revenge_accuracy: 0.95
        threat_assessment: 0.9
        timing_optimization: 0.9

# AI学习和适应配置
learning_settings:
  enabled: false                   # 是否启用学习功能
  adaptation_rate: 0.1            # 适应速率
  memory_retention: 0.8           # 记忆保持率
  experience_weight: 0.3          # 经验权重
  pattern_learning: true          # 模式学习
  opponent_modeling: true         # 对手建模
  strategy_evolution: false       # 策略进化

# AI行为调整参数
behavior_settings:
  speech_frequency: 0.5           # 发言频率
  vote_confidence: 0.7            # 投票信心
  information_sharing: 0.5        # 信息分享倾向
  alliance_tendency: 0.5          # 结盟倾向
  bluff_frequency: 0.3            # 虚张声势频率
  emotional_expression: 0.4       # 情感表达程度
  consistency_level: 0.7          # 行为一致性

# AI团队配置
team_settings:
  balance_personalities: true     # 平衡个性分布
  avoid_duplicate_types: true     # 避免重复类型
  min_difficulty_spread: 2        # 最小难度差异
  max_same_personality: 2         # 最大相同个性数量

  # 推荐的团队组合
  recommended_combinations:
    balanced:
      - { personality: "analytical", difficulty: "normal" }
      - { personality: "social", difficulty: "normal" }
      - { personality: "aggressive", difficulty: "hard" }
      - { personality: "defensive", difficulty: "easy" }
      - { personality: "intuitive", difficulty: "normal" }
      - { personality: "loyal", difficulty: "normal" }

    competitive:
      - { personality: "analytical", difficulty: "expert" }
      - { personality: "deceptive", difficulty: "expert" }
      - { personality: "aggressive", difficulty: "hard" }
      - { personality: "social", difficulty: "hard" }
      - { personality: "intuitive", difficulty: "hard" }
      - { personality: "defensive", difficulty: "normal" }

    casual:
      - { personality: "social", difficulty: "easy" }
      - { personality: "loyal", difficulty: "easy" }
      - { personality: "intuitive", difficulty: "normal" }
      - { personality: "analytical", difficulty: "normal" }
      - { personality: "chaotic", difficulty: "easy" }
      - { personality: "defensive", difficulty: "easy" }

# 性能优化配置
performance_settings:
  decision_timeout: 5.0           # 决策超时时间（秒）
  thinking_simulation: true       # 模拟思考时间
  parallel_processing: true       # 并行处理
  cache_decisions: true           # 缓存决策
  batch_updates: true             # 批量更新

# 调试和监控配置
debug_settings:
  log_decisions: false            # 记录决策过程
  log_personality_effects: false # 记录个性影响
  log_learning_updates: false    # 记录学习更新
  performance_monitoring: true   # 性能监控
  decision_analysis: false       # 决策分析
