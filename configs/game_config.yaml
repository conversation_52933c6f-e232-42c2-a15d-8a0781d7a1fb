# 游戏配置文件
# 包含游戏模式、规则参数、时间限制等所有游戏相关的配置

# 游戏模式配置
game_modes:
  classic:
    name: "经典模式"
    description: "标准的狼人杀游戏，包含基础角色和规则"
    min_players: 6
    max_players: 12
    recommended_players: 8
    difficulty: "normal"
    role_distribution:
      VILLAGER: 4
      WEREWOLF: 2
      SEER: 1
      WITCH: 1
    settings:
      enable_special_roles: true
      discussion_time_limit: 300    # 讨论时间限制（秒）
      voting_time_limit: 60         # 投票时间限制（秒）
      allow_tie_votes: false        # 是否允许平票
      night_action_time: 30         # 夜晚行动时间
      reveal_roles_on_death: true   # 死亡时是否公开角色
      allow_last_words: true        # 是否允许遗言
      hunter_revenge_time: 15       # 猎人复仇时间
    
  simple:
    name: "简单模式"
    description: "只有村民和狼人的简化版本，适合新手"
    min_players: 4
    max_players: 8
    recommended_players: 6
    difficulty: "easy"
    role_distribution:
      VILLAGER: 4
      WEREWOLF: 2
    settings:
      enable_special_roles: false
      discussion_time_limit: 180
      voting_time_limit: 45
      allow_tie_votes: true
      night_action_time: 20
      reveal_roles_on_death: true
      allow_last_words: false
      special_rules:
        no_special_roles: true
        allow_ties: true
    
  complex:
    name: "复杂模式"
    description: "包含所有角色的完整版本，适合高级玩家"
    min_players: 10
    max_players: 16
    recommended_players: 12
    difficulty: "hard"
    role_distribution:
      VILLAGER: 5
      WEREWOLF: 3
      SEER: 1
      WITCH: 1
      GUARD: 1
      HUNTER: 1
    settings:
      enable_special_roles: true
      discussion_time_limit: 400
      voting_time_limit: 90
      allow_tie_votes: false
      night_action_time: 45
      reveal_roles_on_death: true
      allow_last_words: true
      hunter_revenge_time: 20
      special_rules:
        multiple_werewolf_kills: false
        witch_self_save_first_night: true
        guard_consecutive_protection: false
    
  balanced:
    name: "平衡模式"
    description: "经过平衡调整的竞技版本"
    min_players: 8
    max_players: 12
    recommended_players: 10
    difficulty: "normal"
    role_distribution:
      VILLAGER: 4
      WEREWOLF: 3
      SEER: 1
      WITCH: 1
      GUARD: 1
    settings:
      enable_special_roles: true
      discussion_time_limit: 300
      voting_time_limit: 60
      allow_tie_votes: false
      night_action_time: 30
      reveal_roles_on_death: true
      allow_last_words: true
      hunter_revenge_time: 15
    
  quick:
    name: "快速模式"
    description: "时间较短的快节奏游戏"
    min_players: 6
    max_players: 8
    recommended_players: 6
    difficulty: "normal"
    role_distribution:
      VILLAGER: 3
      WEREWOLF: 2
      SEER: 1
    settings:
      enable_special_roles: true
      discussion_time_limit: 120
      voting_time_limit: 30
      allow_tie_votes: true
      night_action_time: 15
      reveal_roles_on_death: true
      allow_last_words: false
      special_rules:
        fast_paced: true
        reduced_discussion_time: true

# 默认游戏设置
default_settings:
  game_mode: "classic"              # 默认游戏模式
  auto_start: false                 # 自动开始游戏
  spectator_mode: true              # 观察者模式
  replay_enabled: true              # 启用回放
  statistics_tracking: true        # 统计追踪
  
  # 时间设置
  time_settings:
    day_phase_time: 300             # 白天阶段时间
    night_phase_time: 60            # 夜晚阶段时间
    voting_phase_time: 60           # 投票阶段时间
    discussion_time_per_player: 30  # 每个玩家讨论时间
    action_timeout: 30              # 行动超时时间
    grace_period: 10                # 宽限期
  
  # 投票设置
  voting_settings:
    allow_abstention: true          # 允许弃权
    require_majority: true          # 需要多数票
    tie_resolution: "revote"        # 平票解决方式: revote, random, no_elimination
    vote_visibility: "hidden"      # 投票可见性: hidden, public, partial
    allow_vote_change: true         # 允许改票
    voting_order: "random"          # 投票顺序: random, clockwise, reverse
  
  # 角色设置
  role_settings:
    random_assignment: true         # 随机分配角色
    role_reveal_timing: "on_death"  # 角色公开时机: never, on_death, immediate
    allow_role_claims: true         # 允许声明角色
    verify_role_claims: false       # 验证角色声明
  
  # 特殊规则设置
  special_rules:
    first_night_immunity: false    # 首夜免疫
    werewolf_chat_enabled: true    # 狼人聊天
    dead_player_chat: false        # 死亡玩家聊天
    spectator_chat: true           # 观察者聊天
    anonymous_voting: false        # 匿名投票
    double_elimination: false      # 双重淘汰

# 胜利条件配置
victory_conditions:
  villager_victory:
    - "all_werewolves_eliminated"   # 所有狼人被淘汰
    - "special_role_victory"        # 特殊角色胜利条件
  
  werewolf_victory:
    - "equal_or_outnumber_villagers" # 狼人数量等于或超过村民
    - "eliminate_all_special_roles"  # 淘汰所有特殊角色
  
  special_victories:
    hunter_revenge: true            # 猎人复仇胜利
    witch_poison_victory: false     # 女巫毒杀胜利
    seer_revelation_bonus: false    # 预言家揭示奖励

# 平衡性配置
balance_settings:
  role_power_levels:
    VILLAGER: 1.0
    WEREWOLF: 2.5
    SEER: 3.0
    WITCH: 2.8
    GUARD: 2.2
    HUNTER: 2.0
  
  team_balance_target: 0.5          # 团队平衡目标（0.5为完全平衡）
  auto_balance_enabled: true        # 自动平衡
  balance_tolerance: 0.1            # 平衡容忍度
  
  # 动态平衡调整
  dynamic_adjustments:
    enabled: false                  # 启用动态调整
    adjustment_frequency: 5         # 调整频率（游戏轮数）
    max_adjustment: 0.1             # 最大调整幅度

# 游戏流程配置
game_flow:
  phases:
    setup:
      duration: 30                  # 设置阶段时长
      allow_player_join: true       # 允许玩家加入
      role_assignment_delay: 5      # 角色分配延迟
    
    night:
      duration: 60                  # 夜晚阶段时长
      action_order:                 # 行动顺序
        - "werewolf_kill"
        - "seer_check"
        - "witch_action"
        - "guard_protect"
      simultaneous_actions: false   # 同时行动
      action_timeout: 30            # 行动超时
    
    day:
      duration: 300                 # 白天阶段时长
      discussion_rounds: 1          # 讨论轮数
      speaking_order: "random"      # 发言顺序
      speaking_time_limit: 60       # 发言时间限制
    
    voting:
      duration: 60                  # 投票阶段时长
      voting_rounds: 1              # 投票轮数
      reveal_votes: false           # 公开投票
      elimination_threshold: 0.5    # 淘汰阈值

# 事件和通知配置
events_settings:
  notifications:
    player_death: true              # 玩家死亡通知
    role_actions: true              # 角色行动通知
    voting_updates: true            # 投票更新通知
    phase_changes: true             # 阶段变化通知
    game_events: true               # 游戏事件通知
  
  sound_effects:
    enabled: true                   # 启用音效
    volume: 0.7                     # 音量
    death_sound: true               # 死亡音效
    action_sound: true              # 行动音效
    notification_sound: true       # 通知音效
  
  animations:
    enabled: true                   # 启用动画
    speed: "normal"                 # 动画速度: slow, normal, fast
    death_animation: true           # 死亡动画
    action_animation: true          # 行动动画
    transition_animation: true     # 转场动画

# 统计和分析配置
statistics_settings:
  track_player_performance: true   # 追踪玩家表现
  track_role_effectiveness: true   # 追踪角色效果
  track_game_balance: true         # 追踪游戏平衡
  track_ai_behavior: true          # 追踪AI行为
  
  metrics:
    win_rate_by_role: true          # 按角色胜率
    survival_rate: true             # 生存率
    voting_accuracy: true           # 投票准确性
    action_effectiveness: true      # 行动有效性
    game_duration: true             # 游戏时长
  
  export_format: "json"             # 导出格式: json, csv, yaml
  auto_export: false                # 自动导出
  export_interval: 10               # 导出间隔（游戏数）

# 调试和开发配置
debug_settings:
  debug_mode: false                 # 调试模式
  verbose_logging: false            # 详细日志
  show_all_roles: false             # 显示所有角色
  skip_night_phase: false           # 跳过夜晚阶段
  auto_actions: false               # 自动行动
  fast_forward: false               # 快进模式
  
  test_scenarios:
    enabled: false                  # 启用测试场景
    scenario_file: "test_scenarios.yaml"
    auto_run: false                 # 自动运行
