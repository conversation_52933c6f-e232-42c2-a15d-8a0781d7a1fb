# 前端和后端服务配置文件
# 管理所有服务的端口、地址、环境变量等配置

# 后端服务配置
backend:
  name: "狼人杀AI游戏后端服务"
  description: "Flask API服务器，提供RESTful API和WebSocket通信"
  
  # 服务器配置
  server:
    host: "0.0.0.0"              # 监听地址
    port: 8000                   # 服务端口
    debug: true                  # 调试模式
    threaded: true               # 多线程模式
    
  # Flask应用配置
  flask:
    secret_key: "wolfkill-secret-key-change-in-production"
    json_sort_keys: false
    jsonify_prettyprint_regular: true
    max_content_length: 16777216  # 16MB
    
  # CORS配置
  cors:
    origins:
      - "http://localhost:3000"   # 前端开发服务器
      - "http://localhost:3001"   # 前端备用端口
      - "http://localhost:3002"   # 前端备用端口
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["Content-Type", "Authorization"]
    supports_credentials: true
    
  # WebSocket配置
  websocket:
    cors_allowed_origins:
      - "http://localhost:3000"
      - "http://localhost:3001"
      - "http://localhost:3002"
    async_mode: "threading"       # threading, eventlet, gevent
    ping_timeout: 60
    ping_interval: 25
    
  # API配置
  api:
    version: "v1"
    prefix: "/api"
    rate_limiting:
      enabled: false
      default_limits: "100 per hour"
    
    # API端点配置
    endpoints:
      health: "/health"
      games: "/games"
      players: "/players"
      chat: "/chat"
      
  # 数据库配置（如果需要）
  database:
    enabled: false
    type: "sqlite"               # sqlite, postgresql, mysql
    url: "sqlite:///wolfkill.db"
    pool_size: 5
    max_overflow: 10
    
  # 缓存配置
  cache:
    enabled: true
    type: "memory"               # memory, redis, memcached
    default_timeout: 300
    
  # 日志配置
  logging:
    level: "INFO"                # DEBUG, INFO, WARNING, ERROR
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: true
    file_path: "logs/backend.log"
    max_bytes: 10485760          # 10MB
    backup_count: 5
    
  # 安全配置
  security:
    csrf_enabled: false
    session_protection: "strong"
    permanent_session_lifetime: 3600  # 1小时
    
  # 性能配置
  performance:
    request_timeout: 30
    max_workers: 4
    keep_alive_timeout: 5
    
  # 环境变量
  environment_variables:
    FLASK_ENV: "development"
    FLASK_DEBUG: "1"
    PYTHONPATH: "${PROJECT_ROOT}/src"

# 前端服务配置
frontend:
  name: "狼人杀AI游戏前端应用"
  description: "React应用，提供现代化的用户界面"
  
  # 开发服务器配置
  dev_server:
    port: 3000                   # 开发服务器端口
    host: "localhost"            # 开发服务器地址
    open: true                   # 自动打开浏览器
    hot_reload: true             # 热重载
    
  # 构建配置
  build:
    output_dir: "build"
    public_path: "/"
    source_map: true
    minify: true
    
  # API连接配置
  api_connection:
    base_url: "http://localhost:8000/api"
    websocket_url: "http://localhost:8000"
    timeout: 10000               # 请求超时时间（毫秒）
    retry_attempts: 3
    retry_delay: 1000            # 重试延迟（毫秒）
    
  # 环境变量
  environment_variables:
    # React应用环境变量（必须以REACT_APP_开头）
    REACT_APP_API_URL: "http://localhost:8000/api"
    REACT_APP_WEBSOCKET_URL: "http://localhost:8000"
    REACT_APP_VERSION: "1.0.0"
    REACT_APP_DEBUG: "true"
    
    # Node.js环境变量
    NODE_ENV: "development"
    GENERATE_SOURCEMAP: "true"
    BROWSER: "chrome"            # 默认浏览器
    
  # 代理配置（用于开发环境）
  proxy:
    enabled: true
    target: "http://localhost:8000"
    change_origin: true
    secure: false
    
  # PWA配置
  pwa:
    enabled: false
    name: "狼人杀AI游戏"
    short_name: "Wolfkill"
    theme_color: "#000000"
    background_color: "#ffffff"
    
  # 性能配置
  performance:
    bundle_analyzer: false
    code_splitting: true
    lazy_loading: true
    image_optimization: true

# 开发环境配置
development:
  name: "开发环境"
  
  # 服务启动顺序
  startup_order:
    - "backend"
    - "frontend"
    
  # 健康检查
  health_checks:
    backend:
      url: "http://localhost:8000/api/health"
      interval: 30
      timeout: 5
      retries: 3
      
    frontend:
      url: "http://localhost:3000"
      interval: 30
      timeout: 5
      retries: 3
      
  # 自动重启配置
  auto_restart:
    enabled: true
    watch_files:
      - "src/**/*.py"
      - "backend/**/*.py"
      - "configs/*.yaml"
      
  # 调试配置
  debugging:
    backend_debug: true
    frontend_debug: true
    verbose_logging: true
    error_overlay: true

# 生产环境配置
production:
  name: "生产环境"
  
  # 后端生产配置
  backend:
    debug: false
    host: "0.0.0.0"
    port: 8000
    workers: 4
    
    # 安全配置
    security:
      csrf_enabled: true
      secure_cookies: true
      https_only: true
      
    # 性能配置
    performance:
      gzip_compression: true
      static_file_caching: true
      database_pool_size: 20
      
  # 前端生产配置
  frontend:
    build_optimization: true
    minification: true
    source_maps: false
    
  # 监控配置
  monitoring:
    enabled: true
    metrics_endpoint: "/metrics"
    health_endpoint: "/health"
    
  # 日志配置
  logging:
    level: "WARNING"
    centralized: true
    log_aggregation: true

# 测试环境配置
testing:
  name: "测试环境"
  
  # 后端测试配置
  backend:
    port: 8001
    database_url: "sqlite:///test_wolfkill.db"
    
  # 前端测试配置
  frontend:
    port: 3001
    api_url: "http://localhost:8001/api"
    
  # 测试数据配置
  test_data:
    mock_games: true
    sample_players: 6
    auto_play: false

# Docker配置
docker:
  enabled: false
  
  # 后端容器配置
  backend_container:
    image: "wolfkill-backend"
    port: 8000
    environment:
      - "FLASK_ENV=production"
      - "DATABASE_URL=******************************/wolfkill"
      
  # 前端容器配置
  frontend_container:
    image: "wolfkill-frontend"
    port: 3000
    environment:
      - "REACT_APP_API_URL=http://backend:8000/api"
      
  # 数据库容器配置
  database_container:
    image: "postgres:13"
    port: 5432
    environment:
      - "POSTGRES_DB=wolfkill"
      - "POSTGRES_USER=wolfkill_user"
      - "POSTGRES_PASSWORD=secure_password"

# 部署配置
deployment:
  strategy: "rolling"            # rolling, blue_green, canary
  
  # 负载均衡配置
  load_balancer:
    enabled: false
    algorithm: "round_robin"     # round_robin, least_connections, ip_hash
    health_check_path: "/api/health"
    
  # SSL/TLS配置
  ssl:
    enabled: false
    certificate_path: "/etc/ssl/certs/wolfkill.crt"
    private_key_path: "/etc/ssl/private/wolfkill.key"
    
  # 备份配置
  backup:
    enabled: false
    schedule: "0 2 * * *"        # 每天凌晨2点
    retention_days: 30
