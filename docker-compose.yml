# Docker Compose配置文件
# 用于容器化部署狼人杀AI游戏

version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: wolfkill-backend
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - WOLFKILL_ENV=${WOLFKILL_ENV:-production}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///wolfkill.db}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
      - backend_data:/app/data
    depends_on:
      - redis
      - postgres
    networks:
      - wolfkill-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: wolfkill-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000/api}
      - REACT_APP_WEBSOCKET_URL=${REACT_APP_WEBSOCKET_URL:-http://localhost:8000}
    volumes:
      - ./configs:/app/configs:ro
    depends_on:
      - backend
    networks:
      - wolfkill-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: wolfkill-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - wolfkill-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: wolfkill-postgres
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-wolfkill}
      - POSTGRES_USER=${POSTGRES_USER:-wolfkill_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - wolfkill-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-wolfkill_user} -d ${POSTGRES_DB:-wolfkill}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: wolfkill-nginx
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - wolfkill-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: wolfkill-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - wolfkill-network
    restart: unless-stopped
    profiles:
      - monitoring

  # 日志聚合服务 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: wolfkill-grafana
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - wolfkill-network
    restart: unless-stopped
    profiles:
      - monitoring

# 网络配置
networks:
  wolfkill-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  backend_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
