#!/usr/bin/env python3
"""
配置管理系统使用示例
演示如何使用新的配置管理系统
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.config_manager import config_manager, get_config, set_config


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础配置使用示例 ===")
    
    # 加载AI配置
    ai_config = get_config("ai_config")
    print(f"AI配置已加载，包含 {len(ai_config)} 个顶级配置项")
    
    # 获取特定配置值
    difficulty_levels = get_config("ai_config", "difficulty_levels")
    print(f"难度等级数量: {len(difficulty_levels)}")
    
    # 获取嵌套配置值
    normal_accuracy = get_config("ai_config", "difficulty_levels.normal.parameters.decision_accuracy")
    print(f"普通难度决策准确性: {normal_accuracy}")
    
    # 获取默认值
    unknown_value = get_config("ai_config", "unknown_key", default="默认值")
    print(f"未知键的默认值: {unknown_value}")
    
    print()


def example_game_config():
    """游戏配置示例"""
    print("=== 游戏配置使用示例 ===")
    
    # 获取游戏模式
    game_modes = get_config("game_config", "game_modes")
    print(f"可用游戏模式: {list(game_modes.keys())}")
    
    # 获取经典模式配置
    classic_mode = get_config("game_config", "game_modes.classic")
    print(f"经典模式推荐玩家数: {classic_mode['recommended_players']}")
    print(f"经典模式角色分配: {classic_mode['role_distribution']}")
    
    # 获取默认设置
    default_settings = get_config("game_config", "default_settings")
    print(f"默认游戏模式: {default_settings['game_mode']}")
    
    print()


def example_llm_config():
    """LLM配置示例"""
    print("=== LLM配置使用示例 ===")
    
    # 获取提供者配置
    providers = get_config("llm_config", "providers")
    print(f"可用LLM提供者: {list(providers.keys())}")
    
    # 获取默认提供者
    default_provider = get_config("llm_config", "default_provider")
    print(f"默认提供者: {default_provider}")
    
    # 获取Qwen3配置
    qwen3_config = get_config("llm_config", "providers.qwen3_30b")
    print(f"Qwen3模型名称: {qwen3_config['model_name']}")
    print(f"Qwen3最大令牌数: {qwen3_config['generation_params']['max_tokens']}")
    
    print()


def example_dynamic_config():
    """动态配置修改示例"""
    print("=== 动态配置修改示例 ===")
    
    # 修改AI配置
    original_temp = get_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold")
    print(f"原始怀疑阈值: {original_temp}")
    
    # 设置新值（不保存到文件）
    set_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold", 0.8, save=False)
    new_temp = get_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold")
    print(f"修改后怀疑阈值: {new_temp}")
    
    # 恢复原值
    set_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold", original_temp, save=False)
    restored_temp = get_config("ai_config", "role_strategies.villager.parameters.suspicion_threshold")
    print(f"恢复后怀疑阈值: {restored_temp}")
    
    print()


def example_environment_override():
    """环境变量覆盖示例"""
    print("=== 环境变量覆盖示例 ===")
    
    # 设置环境变量
    os.environ["WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ENABLED"] = "true"
    os.environ["WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ADAPTATION_RATE"] = "0.2"
    
    # 重新加载配置以应用环境变量
    config_manager.reload_config("ai_config")
    
    # 检查环境变量是否生效
    learning_enabled = get_config("ai_config", "learning_settings.enabled")
    adaptation_rate = get_config("ai_config", "learning_settings.adaptation_rate")
    
    print(f"学习功能启用状态 (环境变量): {learning_enabled}")
    print(f"适应速率 (环境变量): {adaptation_rate}")
    
    # 清理环境变量
    del os.environ["WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ENABLED"]
    del os.environ["WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ADAPTATION_RATE"]
    
    print()


def example_config_validation():
    """配置验证示例"""
    print("=== 配置验证示例 ===")
    
    # 验证所有配置
    validation_results = config_manager.validate_all_configs()
    
    for config_name, result in validation_results.items():
        status = "✓" if result["valid"] else "✗"
        print(f"{status} {config_name}: {'通过' if result['valid'] else '失败'}")
        
        if not result["valid"]:
            for error in result["errors"]:
                print(f"  错误: {error}")
    
    print()


def example_config_info():
    """配置信息示例"""
    print("=== 配置信息示例 ===")
    
    configs = ["ai_config", "game_config", "llm_config"]
    
    for config_name in configs:
        info = config_manager.get_config_info(config_name)
        print(f"配置: {config_name}")
        print(f"  已加载: {'是' if info['loaded'] else '否'}")
        print(f"  文件路径: {info['file_path']}")
        print(f"  模式可用: {'是' if info['schema_available'] else '否'}")
        
        if 'schema' in info:
            schema = info['schema']
            print(f"  描述: {schema['description']}")
            print(f"  必需字段: {len(schema['required_fields'])}")
            print(f"  可选字段: {len(schema['optional_fields'])}")
        print()


def example_ai_strategy_config():
    """AI策略配置使用示例"""
    print("=== AI策略配置使用示例 ===")
    
    # 获取角色策略配置
    role_strategies = get_config("ai_config", "role_strategies")
    
    for role, strategy in role_strategies.items():
        print(f"{role.upper()} 策略:")
        print(f"  名称: {strategy['name']}")
        
        # 显示主要参数
        params = strategy['parameters']
        key_params = list(params.keys())[:3]  # 显示前3个参数
        for param in key_params:
            print(f"  {param}: {params[param]}")
        
        # 显示难度修正
        if 'difficulty_modifiers' in strategy:
            modifiers = strategy['difficulty_modifiers']
            print(f"  难度修正: {list(modifiers.keys())}")
        
        print()


def example_team_configuration():
    """团队配置示例"""
    print("=== 团队配置示例 ===")
    
    # 获取团队设置
    team_settings = get_config("ai_config", "team_settings")
    print(f"平衡个性分布: {team_settings['balance_personalities']}")
    print(f"避免重复类型: {team_settings['avoid_duplicate_types']}")
    print(f"最大相同个性数量: {team_settings['max_same_personality']}")
    
    # 获取推荐组合
    combinations = team_settings['recommended_combinations']
    print(f"\n推荐团队组合:")
    
    for combo_name, combo_config in combinations.items():
        print(f"  {combo_name.upper()}:")
        for player_config in combo_config:
            print(f"    - {player_config['personality']} ({player_config['difficulty']})")
    
    print()


def example_performance_settings():
    """性能设置示例"""
    print("=== 性能设置示例 ===")
    
    # AI性能设置
    ai_performance = get_config("ai_config", "performance_settings")
    print("AI性能设置:")
    for key, value in ai_performance.items():
        print(f"  {key}: {value}")
    
    # LLM性能设置
    llm_performance = get_config("llm_config", "usage_strategies.load_balancing")
    print("\nLLM负载均衡设置:")
    for key, value in llm_performance.items():
        print(f"  {key}: {value}")
    
    print()


def main():
    """主函数"""
    print("🎮 狼人杀AI游戏 - 配置管理系统使用示例")
    print("=" * 60)
    print()
    
    try:
        # 基础使用
        example_basic_usage()
        
        # 各类配置示例
        example_game_config()
        example_llm_config()
        
        # 高级功能
        example_dynamic_config()
        example_environment_override()
        
        # 验证和信息
        example_config_validation()
        example_config_info()
        
        # 专门用途示例
        example_ai_strategy_config()
        example_team_configuration()
        example_performance_settings()
        
        print("✅ 所有示例执行完成！")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
