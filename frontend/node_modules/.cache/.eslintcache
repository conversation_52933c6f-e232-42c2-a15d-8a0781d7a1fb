[{"/private/peiwy/work/ai_projects/wolfkill/frontend/src/index.tsx": "1", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx": "2", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/theme.ts": "3", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx": "4", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx": "5", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx": "6", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/Header.tsx": "7", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx": "8", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/types/index.ts": "9", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/AnimatedComponents.tsx": "10", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx": "11", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx": "12", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx": "13", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/services/api.ts": "14", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/hooks/useWebSocket.ts": "15", "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/animations.ts": "16"}, {"size": 251, "mtime": 1752749675583, "results": "17", "hashOfConfig": "18"}, {"size": 9981, "mtime": 1752801493466, "results": "19", "hashOfConfig": "18"}, {"size": 3653, "mtime": 1752749245274, "results": "20", "hashOfConfig": "18"}, {"size": 8374, "mtime": 1752801607431, "results": "21", "hashOfConfig": "18"}, {"size": 14139, "mtime": 1752801585455, "results": "22", "hashOfConfig": "18"}, {"size": 8039, "mtime": 1752750694638, "results": "23", "hashOfConfig": "18"}, {"size": 4755, "mtime": 1752749466465, "results": "24", "hashOfConfig": "18"}, {"size": 10983, "mtime": 1752800491075, "results": "25", "hashOfConfig": "18"}, {"size": 1942, "mtime": 1752749204932, "results": "26", "hashOfConfig": "18"}, {"size": 6365, "mtime": 1752801870520, "results": "27", "hashOfConfig": "18"}, {"size": 10354, "mtime": 1752750205325, "results": "28", "hashOfConfig": "18"}, {"size": 15092, "mtime": 1752750298893, "results": "29", "hashOfConfig": "18"}, {"size": 7230, "mtime": 1752801974385, "results": "30", "hashOfConfig": "18"}, {"size": 6033, "mtime": 1752801253932, "results": "31", "hashOfConfig": "18"}, {"size": 4285, "mtime": 1752801266865, "results": "32", "hashOfConfig": "18"}, {"size": 4681, "mtime": 1752800900787, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "93m9yp", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/private/peiwy/work/ai_projects/wolfkill/frontend/src/index.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/App.tsx", ["82", "83"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/theme.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/PlayerList.tsx", ["84"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/GameBoard.tsx", ["85", "86", "87"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/ChatPanel.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/Header.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/contexts/GameContext.tsx", ["88"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/types/index.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/AnimatedComponents.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/VoteDialog.tsx", ["89"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/dialogs/ActionDialog.tsx", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx", ["90", "91", "92", "93", "94", "95", "96", "97"], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/services/api.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/hooks/useWebSocket.ts", [], [], "/private/peiwy/work/ai_projects/wolfkill/frontend/src/styles/animations.ts", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 152, "column": 5, "nodeType": "100", "messageId": "101", "endLine": 152, "endColumn": 21}, {"ruleId": "98", "severity": 1, "message": "102", "line": 244, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 244, "endColumn": 17}, {"ruleId": "98", "severity": 1, "message": "103", "line": 5, "column": 25, "nodeType": "100", "messageId": "101", "endLine": 5, "endColumn": 31}, {"ruleId": "98", "severity": 1, "message": "104", "line": 231, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 231, "endColumn": 24}, {"ruleId": "98", "severity": 1, "message": "105", "line": 231, "column": 26, "nodeType": "100", "messageId": "101", "endLine": 231, "endColumn": 43}, {"ruleId": "106", "severity": 1, "message": "107", "line": 251, "column": 6, "nodeType": "108", "endLine": 251, "endColumn": 47, "suggestions": "109"}, {"ruleId": "98", "severity": 1, "message": "110", "line": 2, "column": 46, "nodeType": "100", "messageId": "101", "endLine": 2, "endColumn": 62}, {"ruleId": "98", "severity": 1, "message": "111", "line": 4, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 4, "endColumn": 22}, {"ruleId": "98", "severity": 1, "message": "112", "line": 3, "column": 10, "nodeType": "100", "messageId": "101", "endLine": 3, "endColumn": 20}, {"ruleId": "98", "severity": 1, "message": "113", "line": 6, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 6, "endColumn": 24}, {"ruleId": "98", "severity": 1, "message": "114", "line": 91, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 91, "endColumn": 20}, {"ruleId": "98", "severity": 1, "message": "115", "line": 130, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 130, "endColumn": 21}, {"ruleId": "98", "severity": 1, "message": "116", "line": 175, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 175, "endColumn": 21}, {"ruleId": "98", "severity": 1, "message": "117", "line": 219, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 219, "endColumn": 22}, {"ruleId": "98", "severity": 1, "message": "118", "line": 259, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 259, "endColumn": 22}, {"ruleId": "98", "severity": 1, "message": "119", "line": 299, "column": 7, "nodeType": "100", "messageId": "101", "endLine": 299, "endColumn": 21}, "@typescript-eslint/no-unused-vars", "'selectedPlayerId' is assigned a value but never used.", "Identifier", "unusedVar", "'TestApp' is defined but never used.", "'FadeIn' is defined but never used.", "'selectedAction' is assigned a value but never used.", "'setSelectedAction' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'gameState'. Either include it or remove the dependency array.", "ArrayExpression", ["120"], "'WebSocketMessage' is defined but never used.", "'getRoleColor' is defined but never used.", "'animations' is defined but never used.", "'cardFlipAnimation' is assigned a value but never used.", "'voteAnimation' is assigned a value but never used.", "'deathAnimation' is assigned a value but never used.", "'skillAnimation' is assigned a value but never used.", "'phaseTransition' is assigned a value but never used.", "'bubbleAnimation' is assigned a value but never used.", "'timerAnimation' is assigned a value but never used.", {"desc": "121", "fix": "122"}, "Update the dependencies array to be: [gameState, gameState.current_phase, previousPhase]", {"range": "123", "text": "124"}, [8496, 8537], "[gameState, gameState.current_phase, previousPhase]"]