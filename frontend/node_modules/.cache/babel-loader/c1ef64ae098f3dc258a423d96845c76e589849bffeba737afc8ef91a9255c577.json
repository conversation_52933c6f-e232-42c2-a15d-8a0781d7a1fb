{"ast": null, "code": "var _jsxFileName = \"/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled, { css, keyframes } from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// 玩家卡片翻转动画\nconst cardFlipAnimation = keyframes`\n  0% {\n    transform: rotateY(0);\n  }\n  50% {\n    transform: rotateY(90deg);\n  }\n  100% {\n    transform: rotateY(0);\n  }\n`;\nconst PlayerCardContainer = styled.div`\n  perspective: 1000px;\n  transition: all 0.6s ease-in-out;\n\n  ${props => props.isRevealed && css`\n    .card-back {\n      transform: rotateY(180deg);\n    }\n    .card-front {\n      transform: rotateY(0);\n    }\n  `}\n`;\n_c = PlayerCardContainer;\nconst CardSide = styled.div`\n  backface-visibility: hidden;\n  transition: transform 0.6s;\n\n  &.card-front {\n    transform: rotateY(0);\n  }\n\n  &.card-back {\n    transform: rotateY(180deg);\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n`;\n_c2 = CardSide;\nexport const PlayerCardFlip = ({\n  children,\n  backContent,\n  isRevealed = false,\n  onFlipComplete\n}) => {\n  _s();\n  const [isFlipping, setIsFlipping] = useState(false);\n  useEffect(() => {\n    if (isRevealed) {\n      setIsFlipping(true);\n      const timer = setTimeout(() => {\n        setIsFlipping(false);\n        onFlipComplete === null || onFlipComplete === void 0 ? void 0 : onFlipComplete();\n      }, 600);\n      return () => clearTimeout(timer);\n    }\n  }, [isRevealed, onFlipComplete]);\n  return /*#__PURE__*/_jsxDEV(PlayerCardContainer, {\n    isFlipping: isFlipping,\n    isRevealed: isRevealed,\n    children: [/*#__PURE__*/_jsxDEV(CardSide, {\n      className: \"card-front\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), backContent && /*#__PURE__*/_jsxDEV(CardSide, {\n      className: \"card-back\",\n      children: backContent\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n\n// 投票动画\n_s(PlayerCardFlip, \"H6EA+aycpoAKty5vWHxphFURVEI=\");\n_c3 = PlayerCardFlip;\nconst voteAnimation = keyframes`\n  0% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.2) rotate(5deg);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n  }\n`;\nconst VoteContainer = styled.div`\n  transition: all 0.5s ease-in-out;\n  ${props => props.isVoting && css`\n    transform: scale(1.05);\n  `}\n`;\n_c4 = VoteContainer;\nexport const VoteAnimation = ({\n  children,\n  isVoting = false,\n  className\n}) => /*#__PURE__*/_jsxDEV(VoteContainer, {\n  isVoting: isVoting,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 124,\n  columnNumber: 3\n}, this);\n\n// 死亡动画\n_c5 = VoteAnimation;\nconst deathAnimation = keyframes`\n  0% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n  50% {\n    transform: scale(0.9) rotate(-2deg);\n    opacity: 0.7;\n    filter: grayscale(50%);\n  }\n  100% {\n    transform: scale(0.95) rotate(0deg);\n    opacity: 0.5;\n    filter: grayscale(100%);\n  }\n`;\nconst DeathContainer = styled.div`\n  transition: all 0.5s ease;\n\n  ${props => props.isDead && css`\n    opacity: 0.5;\n    filter: grayscale(100%);\n    pointer-events: none;\n  `}\n`;\n_c6 = DeathContainer;\nexport const DeathAnimation = ({\n  children,\n  isDead = false,\n  className\n}) => /*#__PURE__*/_jsxDEV(DeathContainer, {\n  isDead: isDead,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 169,\n  columnNumber: 3\n}, this);\n\n// 技能使用动画\n_c7 = DeathAnimation;\nconst skillAnimation = keyframes`\n  0% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);\n  }\n  50% {\n    transform: scale(1.05);\n    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0.3);\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: 0 0 0 20px rgba(52, 152, 219, 0);\n  }\n`;\nconst SkillContainer = styled.div`\n  transition: all 0.8s ease-out;\n  ${props => props.isActive && css`\n    transform: scale(1.05);\n    ${props.skillColor && css`\n      box-shadow: 0 0 10px ${props.skillColor}70;\n    `}\n  `}\n`;\n_c8 = SkillContainer;\nexport const SkillAnimation = ({\n  children,\n  isActive = false,\n  skillColor,\n  className\n}) => /*#__PURE__*/_jsxDEV(SkillContainer, {\n  isActive: isActive,\n  skillColor: skillColor,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 213,\n  columnNumber: 3\n}, this);\n\n// 阶段切换动画\n_c9 = SkillAnimation;\nconst phaseTransition = keyframes`\n  0% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n  50% {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\nconst PhaseContainer = styled.div`\n  ${props => props.isTransitioning && css`\n    animation: ${css`${phaseTransition} 1s ease-in-out`};\n  `}\n`;\n_c0 = PhaseContainer;\nexport const PhaseTransition = ({\n  children,\n  isTransitioning = false,\n  className\n}) => /*#__PURE__*/_jsxDEV(PhaseContainer, {\n  isTransitioning: isTransitioning,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 251,\n  columnNumber: 3\n}, this);\n\n// 消息气泡动画\n_c1 = PhaseTransition;\nconst bubbleAnimation = keyframes`\n  0% {\n    transform: scale(0) translateY(20px);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.1) translateY(-5px);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1) translateY(0);\n    opacity: 1;\n  }\n`;\nconst BubbleContainer = styled.div`\n  ${props => props.isAppearing && css`\n    animation: ${css`${bubbleAnimation} 0.4s ease-out`};\n  `}\n`;\n_c10 = BubbleContainer;\nexport const MessageBubble = ({\n  children,\n  isAppearing = false,\n  className\n}) => /*#__PURE__*/_jsxDEV(BubbleContainer, {\n  isAppearing: isAppearing,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 289,\n  columnNumber: 3\n}, this);\n\n// 计时器动画\n_c11 = MessageBubble;\nconst timerAnimation = keyframes`\n  0% {\n    stroke-dasharray: 0 100;\n  }\n  100% {\n    stroke-dasharray: 100 100;\n  }\n`;\nconst TimerContainer = styled.div`\n  position: relative;\n  display: inline-block;\n`;\n_c12 = TimerContainer;\nconst TimerSvg = styled.svg`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n\n  circle {\n    fill: none;\n    stroke: ${props => props.theme.colors.primary};\n    stroke-width: 2;\n    stroke-linecap: round;\n    transform: rotate(-90deg);\n    transform-origin: 50% 50%;\n\n    ${props => props.isActive && css`\n      animation: ${css`${timerAnimation} ${props.duration || 30}s linear forwards`};\n    `}\n  }\n`;\n_c13 = TimerSvg;\nexport const TimerAnimation = ({\n  children,\n  duration = 30,\n  isActive = false,\n  className\n}) => /*#__PURE__*/_jsxDEV(TimerContainer, {\n  className: className,\n  children: [children, /*#__PURE__*/_jsxDEV(TimerSvg, {\n    duration: duration,\n    isActive: isActive,\n    viewBox: \"0 0 40 40\",\n    children: /*#__PURE__*/_jsxDEV(\"circle\", {\n      cx: \"20\",\n      cy: \"20\",\n      r: \"18\",\n      strokeDasharray: \"0 100\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 345,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 343,\n  columnNumber: 3\n}, this);\n_c14 = TimerAnimation;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"PlayerCardContainer\");\n$RefreshReg$(_c2, \"CardSide\");\n$RefreshReg$(_c3, \"PlayerCardFlip\");\n$RefreshReg$(_c4, \"VoteContainer\");\n$RefreshReg$(_c5, \"VoteAnimation\");\n$RefreshReg$(_c6, \"DeathContainer\");\n$RefreshReg$(_c7, \"DeathAnimation\");\n$RefreshReg$(_c8, \"SkillContainer\");\n$RefreshReg$(_c9, \"SkillAnimation\");\n$RefreshReg$(_c0, \"PhaseContainer\");\n$RefreshReg$(_c1, \"PhaseTransition\");\n$RefreshReg$(_c10, \"BubbleContainer\");\n$RefreshReg$(_c11, \"MessageBubble\");\n$RefreshReg$(_c12, \"TimerContainer\");\n$RefreshReg$(_c13, \"TimerSvg\");\n$RefreshReg$(_c14, \"TimerAnimation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "css", "keyframes", "jsxDEV", "_jsxDEV", "cardFlipAnimation", "PlayerCardContainer", "div", "props", "isRevealed", "_c", "CardSide", "_c2", "PlayerCardFlip", "children", "backContent", "onFlipComplete", "_s", "isFlipping", "setIsFlipping", "timer", "setTimeout", "clearTimeout", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c3", "voteAnimation", "VoteC<PERSON>r", "isVoting", "_c4", "VoteAnimation", "_c5", "deathAnimation", "Death<PERSON><PERSON><PERSON>", "isDead", "_c6", "DeathAnimation", "_c7", "skillAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "skillColor", "_c8", "SkillAnimation", "_c9", "phaseTransition", "PhaseContainer", "isTransitioning", "_c0", "PhaseTransition", "_c1", "bubbleAnimation", "B<PERSON>bleContainer", "isAppearing", "_c10", "MessageBubble", "_c11", "timerAnimation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c12", "TimerSvg", "svg", "theme", "colors", "primary", "duration", "_c13", "TimerAnimation", "viewBox", "cx", "cy", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c14", "$RefreshReg$"], "sources": ["/private/peiwy/work/ai_projects/wolfkill/frontend/src/components/animations/GameAnimations.tsx"], "sourcesContent": ["import React, { ReactNode, useState, useEffect } from 'react';\nimport styled, { css, keyframes } from 'styled-components';\nimport { animations } from '../../styles/animations';\n\n// 玩家卡片翻转动画\nconst cardFlipAnimation = keyframes`\n  0% {\n    transform: rotateY(0);\n  }\n  50% {\n    transform: rotateY(90deg);\n  }\n  100% {\n    transform: rotateY(0);\n  }\n`;\n\nconst PlayerCardContainer = styled.div<{ isFlipping?: boolean; isRevealed?: boolean }>`\n  perspective: 1000px;\n  transition: all 0.6s ease-in-out;\n\n  ${props => props.isRevealed && css`\n    .card-back {\n      transform: rotateY(180deg);\n    }\n    .card-front {\n      transform: rotateY(0);\n    }\n  `}\n`;\n\nconst CardSide = styled.div`\n  backface-visibility: hidden;\n  transition: transform 0.6s;\n\n  &.card-front {\n    transform: rotateY(0);\n  }\n\n  &.card-back {\n    transform: rotateY(180deg);\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n`;\n\ninterface PlayerCardFlipProps {\n  children: ReactNode;\n  backContent?: ReactNode;\n  isRevealed?: boolean;\n  onFlipComplete?: () => void;\n}\n\nexport const PlayerCardFlip: React.FC<PlayerCardFlipProps> = ({\n  children,\n  backContent,\n  isRevealed = false,\n  onFlipComplete\n}) => {\n  const [isFlipping, setIsFlipping] = useState(false);\n\n  useEffect(() => {\n    if (isRevealed) {\n      setIsFlipping(true);\n      const timer = setTimeout(() => {\n        setIsFlipping(false);\n        onFlipComplete?.();\n      }, 600);\n      return () => clearTimeout(timer);\n    }\n  }, [isRevealed, onFlipComplete]);\n\n  return (\n    <PlayerCardContainer isFlipping={isFlipping} isRevealed={isRevealed}>\n      <CardSide className=\"card-front\">\n        {children}\n      </CardSide>\n      {backContent && (\n        <CardSide className=\"card-back\">\n          {backContent}\n        </CardSide>\n      )}\n    </PlayerCardContainer>\n  );\n};\n\n// 投票动画\nconst voteAnimation = keyframes`\n  0% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.2) rotate(5deg);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n  }\n`;\n\nconst VoteContainer = styled.div<{ isVoting?: boolean }>`\n  transition: all 0.5s ease-in-out;\n  ${props => props.isVoting && css`\n    transform: scale(1.05);\n  `}\n`;\n\ninterface VoteAnimationProps {\n  children: ReactNode;\n  isVoting?: boolean;\n  className?: string;\n}\n\nexport const VoteAnimation: React.FC<VoteAnimationProps> = ({\n  children,\n  isVoting = false,\n  className\n}) => (\n  <VoteContainer isVoting={isVoting} className={className}>\n    {children}\n  </VoteContainer>\n);\n\n// 死亡动画\nconst deathAnimation = keyframes`\n  0% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n  50% {\n    transform: scale(0.9) rotate(-2deg);\n    opacity: 0.7;\n    filter: grayscale(50%);\n  }\n  100% {\n    transform: scale(0.95) rotate(0deg);\n    opacity: 0.5;\n    filter: grayscale(100%);\n  }\n`;\n\nconst DeathContainer = styled.div<{ isDead?: boolean }>`\n  transition: all 0.5s ease;\n\n  ${props => props.isDead && css`\n    opacity: 0.5;\n    filter: grayscale(100%);\n    pointer-events: none;\n  `}\n`;\n\ninterface DeathAnimationProps {\n  children: ReactNode;\n  isDead?: boolean;\n  className?: string;\n}\n\nexport const DeathAnimation: React.FC<DeathAnimationProps> = ({\n  children,\n  isDead = false,\n  className\n}) => (\n  <DeathContainer isDead={isDead} className={className}>\n    {children}\n  </DeathContainer>\n);\n\n// 技能使用动画\nconst skillAnimation = keyframes`\n  0% {\n    transform: scale(1);\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);\n  }\n  50% {\n    transform: scale(1.05);\n    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0.3);\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: 0 0 0 20px rgba(52, 152, 219, 0);\n  }\n`;\n\nconst SkillContainer = styled.div<{ isActive?: boolean; skillColor?: string }>`\n  transition: all 0.8s ease-out;\n  ${props => props.isActive && css`\n    transform: scale(1.05);\n    ${props.skillColor && css`\n      box-shadow: 0 0 10px ${props.skillColor}70;\n    `}\n  `}\n`;\n\ninterface SkillAnimationProps {\n  children: ReactNode;\n  isActive?: boolean;\n  skillColor?: string;\n  className?: string;\n}\n\nexport const SkillAnimation: React.FC<SkillAnimationProps> = ({\n  children,\n  isActive = false,\n  skillColor,\n  className\n}) => (\n  <SkillContainer isActive={isActive} skillColor={skillColor} className={className}>\n    {children}\n  </SkillContainer>\n);\n\n// 阶段切换动画\nconst phaseTransition = keyframes`\n  0% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n  50% {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\n\nconst PhaseContainer = styled.div<{ isTransitioning?: boolean }>`\n  ${props => props.isTransitioning && css`\n    animation: ${css`${phaseTransition} 1s ease-in-out`};\n  `}\n`;\n\ninterface PhaseTransitionProps {\n  children: ReactNode;\n  isTransitioning?: boolean;\n  className?: string;\n}\n\nexport const PhaseTransition: React.FC<PhaseTransitionProps> = ({\n  children,\n  isTransitioning = false,\n  className\n}) => (\n  <PhaseContainer isTransitioning={isTransitioning} className={className}>\n    {children}\n  </PhaseContainer>\n);\n\n// 消息气泡动画\nconst bubbleAnimation = keyframes`\n  0% {\n    transform: scale(0) translateY(20px);\n    opacity: 0;\n  }\n  50% {\n    transform: scale(1.1) translateY(-5px);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(1) translateY(0);\n    opacity: 1;\n  }\n`;\n\nconst BubbleContainer = styled.div<{ isAppearing?: boolean }>`\n  ${props => props.isAppearing && css`\n    animation: ${css`${bubbleAnimation} 0.4s ease-out`};\n  `}\n`;\n\ninterface MessageBubbleProps {\n  children: ReactNode;\n  isAppearing?: boolean;\n  className?: string;\n}\n\nexport const MessageBubble: React.FC<MessageBubbleProps> = ({\n  children,\n  isAppearing = false,\n  className\n}) => (\n  <BubbleContainer isAppearing={isAppearing} className={className}>\n    {children}\n  </BubbleContainer>\n);\n\n// 计时器动画\nconst timerAnimation = keyframes`\n  0% {\n    stroke-dasharray: 0 100;\n  }\n  100% {\n    stroke-dasharray: 100 100;\n  }\n`;\n\nconst TimerContainer = styled.div`\n  position: relative;\n  display: inline-block;\n`;\n\nconst TimerSvg = styled.svg<{ duration?: number; isActive?: boolean }>`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n\n  circle {\n    fill: none;\n    stroke: ${props => props.theme.colors.primary};\n    stroke-width: 2;\n    stroke-linecap: round;\n    transform: rotate(-90deg);\n    transform-origin: 50% 50%;\n\n    ${props => props.isActive && css`\n      animation: ${css`${timerAnimation} ${props.duration || 30}s linear forwards`};\n    `}\n  }\n`;\n\ninterface TimerAnimationProps {\n  children: ReactNode;\n  duration?: number; // 秒\n  isActive?: boolean;\n  className?: string;\n}\n\nexport const TimerAnimation: React.FC<TimerAnimationProps> = ({\n  children,\n  duration = 30,\n  isActive = false,\n  className\n}) => (\n  <TimerContainer className={className}>\n    {children}\n    <TimerSvg duration={duration} isActive={isActive} viewBox=\"0 0 40 40\">\n      <circle cx=\"20\" cy=\"20\" r=\"18\" strokeDasharray=\"0 100\" />\n    </TimerSvg>\n  </TimerContainer>\n);"], "mappings": ";;AAAA,OAAOA,KAAK,IAAeC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7D,OAAOC,MAAM,IAAIC,GAAG,EAAEC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3D;AACA,MAAMC,iBAAiB,GAAGH,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMI,mBAAmB,GAAGN,MAAM,CAACO,GAAmD;AACtF;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,UAAU,IAAIR,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACS,EAAA,GAZIJ,mBAAmB;AAczB,MAAMK,QAAQ,GAAGX,MAAM,CAACO,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAhBID,QAAQ;AAyBd,OAAO,MAAME,cAA6C,GAAGA,CAAC;EAC5DC,QAAQ;EACRC,WAAW;EACXN,UAAU,GAAG,KAAK;EAClBO;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIU,UAAU,EAAE;MACdU,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BF,aAAa,CAAC,KAAK,CAAC;QACpBH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,CAAC;MACpB,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,MAAMM,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACX,UAAU,EAAEO,cAAc,CAAC,CAAC;EAEhC,oBACEZ,OAAA,CAACE,mBAAmB;IAACY,UAAU,EAAEA,UAAW;IAACT,UAAU,EAAEA,UAAW;IAAAK,QAAA,gBAClEV,OAAA,CAACO,QAAQ;MAACY,SAAS,EAAC,YAAY;MAAAT,QAAA,EAC7BA;IAAQ;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EACVZ,WAAW,iBACVX,OAAA,CAACO,QAAQ;MAACY,SAAS,EAAC,WAAW;MAAAT,QAAA,EAC5BC;IAAW;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAE1B,CAAC;;AAED;AAAAV,EAAA,CAjCaJ,cAA6C;AAAAe,GAAA,GAA7Cf,cAA6C;AAkC1D,MAAMgB,aAAa,GAAG3B,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM4B,aAAa,GAAG9B,MAAM,CAACO,GAA2B;AACxD;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACuB,QAAQ,IAAI9B,GAAG;AAClC;AACA,GAAG;AACH,CAAC;AAAC+B,GAAA,GALIF,aAAa;AAanB,OAAO,MAAMG,aAA2C,GAAGA,CAAC;EAC1DnB,QAAQ;EACRiB,QAAQ,GAAG,KAAK;EAChBR;AACF,CAAC,kBACCnB,OAAA,CAAC0B,aAAa;EAACC,QAAQ,EAAEA,QAAS;EAACR,SAAS,EAAEA,SAAU;EAAAT,QAAA,EACrDA;AAAQ;EAAAU,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACI,CAChB;;AAED;AAAAO,GAAA,GAVaD,aAA2C;AAWxD,MAAME,cAAc,GAAGjC,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMkC,cAAc,GAAGpC,MAAM,CAACO,GAAyB;AACvD;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAAC6B,MAAM,IAAIpC,GAAG;AAChC;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACqC,GAAA,GARIF,cAAc;AAgBpB,OAAO,MAAMG,cAA6C,GAAGA,CAAC;EAC5DzB,QAAQ;EACRuB,MAAM,GAAG,KAAK;EACdd;AACF,CAAC,kBACCnB,OAAA,CAACgC,cAAc;EAACC,MAAM,EAAEA,MAAO;EAACd,SAAS,EAAEA,SAAU;EAAAT,QAAA,EAClDA;AAAQ;EAAAU,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;;AAED;AAAAa,GAAA,GAVaD,cAA6C;AAW1D,MAAME,cAAc,GAAGvC,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMwC,cAAc,GAAG1C,MAAM,CAACO,GAAgD;AAC9E;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACmC,QAAQ,IAAI1C,GAAG;AAClC;AACA,MAAMO,KAAK,CAACoC,UAAU,IAAI3C,GAAG;AAC7B,6BAA6BO,KAAK,CAACoC,UAAU;AAC7C,KAAK;AACL,GAAG;AACH,CAAC;AAACC,GAAA,GARIH,cAAc;AAiBpB,OAAO,MAAMI,cAA6C,GAAGA,CAAC;EAC5DhC,QAAQ;EACR6B,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVrB;AACF,CAAC,kBACCnB,OAAA,CAACsC,cAAc;EAACC,QAAQ,EAAEA,QAAS;EAACC,UAAU,EAAEA,UAAW;EAACrB,SAAS,EAAEA,SAAU;EAAAT,QAAA,EAC9EA;AAAQ;EAAAU,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;;AAED;AAAAoB,GAAA,GAXaD,cAA6C;AAY1D,MAAME,eAAe,GAAG9C,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM+C,cAAc,GAAGjD,MAAM,CAACO,GAAkC;AAChE,IAAIC,KAAK,IAAIA,KAAK,CAAC0C,eAAe,IAAIjD,GAAG;AACzC,iBAAiBA,GAAG,GAAG+C,eAAe,iBAAiB;AACvD,GAAG;AACH,CAAC;AAACG,GAAA,GAJIF,cAAc;AAYpB,OAAO,MAAMG,eAA+C,GAAGA,CAAC;EAC9DtC,QAAQ;EACRoC,eAAe,GAAG,KAAK;EACvB3B;AACF,CAAC,kBACCnB,OAAA,CAAC6C,cAAc;EAACC,eAAe,EAAEA,eAAgB;EAAC3B,SAAS,EAAEA,SAAU;EAAAT,QAAA,EACpEA;AAAQ;EAAAU,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;;AAED;AAAA0B,GAAA,GAVaD,eAA+C;AAW5D,MAAME,eAAe,GAAGpD,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMqD,eAAe,GAAGvD,MAAM,CAACO,GAA8B;AAC7D,IAAIC,KAAK,IAAIA,KAAK,CAACgD,WAAW,IAAIvD,GAAG;AACrC,iBAAiBA,GAAG,GAAGqD,eAAe,gBAAgB;AACtD,GAAG;AACH,CAAC;AAACG,IAAA,GAJIF,eAAe;AAYrB,OAAO,MAAMG,aAA2C,GAAGA,CAAC;EAC1D5C,QAAQ;EACR0C,WAAW,GAAG,KAAK;EACnBjC;AACF,CAAC,kBACCnB,OAAA,CAACmD,eAAe;EAACC,WAAW,EAAEA,WAAY;EAACjC,SAAS,EAAEA,SAAU;EAAAT,QAAA,EAC7DA;AAAQ;EAAAU,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACM,CAClB;;AAED;AAAAgC,IAAA,GAVaD,aAA2C;AAWxD,MAAME,cAAc,GAAG1D,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM2D,cAAc,GAAG7D,MAAM,CAACO,GAAG;AACjC;AACA;AACA,CAAC;AAACuD,IAAA,GAHID,cAAc;AAKpB,MAAME,QAAQ,GAAG/D,MAAM,CAACgE,GAA8C;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAcxD,KAAK,IAAIA,KAAK,CAACyD,KAAK,CAACC,MAAM,CAACC,OAAO;AACjD;AACA;AACA;AACA;AACA;AACA,MAAM3D,KAAK,IAAIA,KAAK,CAACmC,QAAQ,IAAI1C,GAAG;AACpC,mBAAmBA,GAAG,GAAG2D,cAAc,IAAIpD,KAAK,CAAC4D,QAAQ,IAAI,EAAE,mBAAmB;AAClF,KAAK;AACL;AACA,CAAC;AAACC,IAAA,GAnBIN,QAAQ;AA4Bd,OAAO,MAAMO,cAA6C,GAAGA,CAAC;EAC5DxD,QAAQ;EACRsD,QAAQ,GAAG,EAAE;EACbzB,QAAQ,GAAG,KAAK;EAChBpB;AACF,CAAC,kBACCnB,OAAA,CAACyD,cAAc;EAACtC,SAAS,EAAEA,SAAU;EAAAT,QAAA,GAClCA,QAAQ,eACTV,OAAA,CAAC2D,QAAQ;IAACK,QAAQ,EAAEA,QAAS;IAACzB,QAAQ,EAAEA,QAAS;IAAC4B,OAAO,EAAC,WAAW;IAAAzD,QAAA,eACnEV,OAAA;MAAQoE,EAAE,EAAC,IAAI;MAACC,EAAE,EAAC,IAAI;MAACC,CAAC,EAAC,IAAI;MAACC,eAAe,EAAC;IAAO;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjD,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACG,CACjB;AAACiD,IAAA,GAZWN,cAA6C;AAAA,IAAA5D,EAAA,EAAAE,GAAA,EAAAgB,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAO,IAAA,EAAAO,IAAA;AAAAC,YAAA,CAAAnE,EAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAf,IAAA;AAAAe,YAAA,CAAAR,IAAA;AAAAQ,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}