import React, { ReactNode } from 'react';
import styled, { css, RuleSet } from 'styled-components';
import { animations, getStaggerDelay } from '../../styles/animations';

// 基础动画容器
interface AnimatedContainerProps {
  children: ReactNode;
  animation?: RuleSet<object> | string;
  delay?: string;
  duration?: string;
  className?: string;
}

const AnimatedContainer = styled.div<AnimatedContainerProps>`
  /* 暂时禁用动画以避免styled-components v4兼容性问题 */
  opacity: 1;
  transform: none;
  transition: all 0.3s ease;
`;

// 淡入组件
export const FadeIn: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.fadeIn} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

// 滑入组件
export const SlideInUp: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.slideInUp} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

export const SlideInDown: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.slideInDown} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

export const SlideInLeft: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.slideInLeft} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

export const SlideInRight: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.slideInRight} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

// 缩放组件
export const ScaleIn: React.FC<AnimatedContainerProps> = ({ children, delay, ...props }) => (
  <AnimatedContainer animation={animations.entrance.scaleIn} delay={delay} {...props}>
    {children}
  </AnimatedContainer>
);

// 列表动画组件
interface StaggeredListProps {
  children: ReactNode[];
  staggerDelay?: number;
  animation?: RuleSet<object> | string;
  className?: string;
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 0.1,
  animation = animations.entrance.slideInUp,
  className
}) => (
  <div className={className}>
    {React.Children.map(children, (child, index) => (
      <AnimatedContainer
        key={index}
        animation={animation}
        delay={getStaggerDelay(index, staggerDelay)}
      >
        {child}
      </AnimatedContainer>
    ))}
  </div>
);

// 悬停动画组件
const HoverContainer = styled.div<{ hoverAnimation?: RuleSet<object> | string }>`
  transition: all 0.3s ease;

  &:hover {
    ${props => props.hoverAnimation && css`
      ${props.hoverAnimation};
    `}
  }
`;

interface HoverAnimatedProps {
  children: ReactNode;
  hoverAnimation?: RuleSet<object> | string;
  className?: string;
}

export const HoverAnimated: React.FC<HoverAnimatedProps> = ({
  children,
  hoverAnimation = animations.interaction.shake,
  className
}) => (
  <HoverContainer hoverAnimation={hoverAnimation} className={className}>
    {children}
  </HoverContainer>
);

// 脉冲动画组件
const PulseContainer = styled.div<{ isActive?: boolean }>`
  ${props => props.isActive && css`
    animation: ${animations.loop.pulse};
  `}
`;

interface PulseProps {
  children: ReactNode;
  isActive?: boolean;
  className?: string;
}

export const Pulse: React.FC<PulseProps> = ({ children, isActive = true, className }) => (
  <PulseContainer isActive={isActive} className={className}>
    {children}
  </PulseContainer>
);

// 发光效果组件
const GlowContainer = styled.div<{ isActive?: boolean; color?: string }>`
  ${props => props.isActive && css`
    animation: ${animations.loop.glow};
    ${props.color && css`
      filter: drop-shadow(0 0 10px ${props.color});
    `}
  `}
`;

interface GlowProps {
  children: ReactNode;
  isActive?: boolean;
  color?: string;
  className?: string;
}

export const Glow: React.FC<GlowProps> = ({
  children,
  isActive = true,
  color,
  className
}) => (
  <GlowContainer isActive={isActive} color={color} className={className}>
    {children}
  </GlowContainer>
);

// 旋转加载组件
const SpinContainer = styled.div<{ size?: string }>`
  width: ${props => props.size || '24px'};
  height: ${props => props.size || '24px'};
  border: 2px solid ${props => props.theme.colors.border};
  border-top: 2px solid ${props => props.theme.colors.primary};
  border-radius: 50%;
  animation: ${animations.loop.spin};
`;

interface SpinnerProps {
  size?: string;
  className?: string;
}

export const Spinner: React.FC<SpinnerProps> = ({ size, className }) => (
  <SpinContainer size={size} className={className} />
);

// 心跳动画组件
const HeartbeatContainer = styled.div<{ isActive?: boolean }>`
  ${props => props.isActive && css`
    animation: ${animations.loop.heartbeat};
  `}
`;

interface HeartbeatProps {
  children: ReactNode;
  isActive?: boolean;
  className?: string;
}

export const Heartbeat: React.FC<HeartbeatProps> = ({
  children,
  isActive = true,
  className
}) => (
  <HeartbeatContainer isActive={isActive} className={className}>
    {children}
  </HeartbeatContainer>
);

// 浮动动画组件
const FloatContainer = styled.div<{ isActive?: boolean }>`
  ${props => props.isActive && css`
    animation: ${animations.loop.float};
  `}
`;

interface FloatProps {
  children: ReactNode;
  isActive?: boolean;
  className?: string;
}

export const Float: React.FC<FloatProps> = ({ children, isActive = true, className }) => (
  <FloatContainer isActive={isActive} className={className}>
    {children}
  </FloatContainer>
);

// 打字机效果组件
const TypewriterContainer = styled.div<{ isActive?: boolean }>`
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid ${props => props.theme.colors.primary};

  ${props => props.isActive && css`
    animation:
      ${animations.text.typewriter},
      ${animations.text.blink};
  `}
`;

interface TypewriterProps {
  children: ReactNode;
  isActive?: boolean;
  className?: string;
}

export const Typewriter: React.FC<TypewriterProps> = ({
  children,
  isActive = true,
  className
}) => (
  <TypewriterContainer isActive={isActive} className={className}>
    {children}
  </TypewriterContainer>
);