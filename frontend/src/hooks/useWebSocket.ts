import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { WebSocketMessage } from '../types';

interface UseWebSocketReturn {
  connected: boolean;
  sendMessage: (message: any) => void;
  lastMessage: WebSocketMessage | null;
  connectionError: string | null;
}

const WEBSOCKET_URL = process.env.REACT_APP_WEBSOCKET_URL || 'http://localhost:8000';

export const useWebSocket = (): UseWebSocketReturn => {
  const [connected, setConnected] = useState<boolean>(false);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  // 发送消息
  const sendMessage = useCallback((message: any) => {
    if (socketRef.current && connected) {
      socketRef.current.emit('message', message);
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }, [connected]);

  // 初始化WebSocket连接
  useEffect(() => {
    // 创建socket连接
    const socket = io(WEBSOCKET_URL, {
      transports: ['websocket', 'polling'],
      timeout: 5000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socketRef.current = socket;

    // 连接成功
    socket.on('connect', () => {
      console.log('WebSocket connected');
      setConnected(true);
      setConnectionError(null);
    });

    // 连接断开
    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setConnected(false);
    });

    // 连接错误
    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionError(error.message);
      setConnected(false);
    });

    // 接收消息
    socket.on('message', (data: any) => {
      const message: WebSocketMessage = {
        type: data.type || 'unknown',
        data: data.data || data,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 游戏状态更新
    socket.on('game_state_update', (gameState: any) => {
      const message: WebSocketMessage = {
        type: 'game_state_update',
        data: gameState,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 聊天消息
    socket.on('chat_message', (chatData: any) => {
      const message: WebSocketMessage = {
        type: 'chat_message',
        data: chatData,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 投票更新
    socket.on('vote_update', (voteData: any) => {
      const message: WebSocketMessage = {
        type: 'vote_update',
        data: voteData,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 行动更新
    socket.on('action_update', (actionData: any) => {
      const message: WebSocketMessage = {
        type: 'action_update',
        data: actionData,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 阶段变化
    socket.on('phase_change', (phaseData: any) => {
      const message: WebSocketMessage = {
        type: 'phase_change',
        data: phaseData,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 游戏结束
    socket.on('game_over', (resultData: any) => {
      const message: WebSocketMessage = {
        type: 'game_over',
        data: resultData,
        timestamp: new Date().toISOString()
      };
      setLastMessage(message);
    });

    // 清理函数
    return () => {
      socket.disconnect();
      socketRef.current = null;
    };
  }, []);

  // 重连逻辑
  useEffect(() => {
    if (!connected && socketRef.current) {
      const reconnectTimer = setTimeout(() => {
        if (socketRef.current && !socketRef.current.connected) {
          console.log('Attempting to reconnect...');
          socketRef.current.connect();
        }
      }, 3000);

      return () => clearTimeout(reconnectTimer);
    }
  }, [connected]);

  return {
    connected,
    sendMessage,
    lastMessage,
    connectionError
  };
};