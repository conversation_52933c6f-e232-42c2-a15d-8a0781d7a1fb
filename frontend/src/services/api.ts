import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { GameState, GameConfig, Vote, Action, ApiResponse } from '../types';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

class GameAPI {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证token等
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        console.error('API Error:', error);

        // 处理网络错误
        if (!error.response) {
          return Promise.reject(new Error('网络连接失败，请检查网络设置'));
        }

        // 处理HTTP错误
        const { status, data } = error.response;
        let errorMessage = '请求失败';

        switch (status) {
          case 400:
            errorMessage = data?.message || '请求参数错误';
            break;
          case 401:
            errorMessage = '未授权访问';
            // 清除本地token
            localStorage.removeItem('auth_token');
            break;
          case 403:
            errorMessage = '访问被拒绝';
            break;
          case 404:
            errorMessage = '请求的资源不存在';
            break;
          case 500:
            errorMessage = '服务器内部错误';
            break;
          default:
            errorMessage = data?.message || `请求失败 (${status})`;
        }

        return Promise.reject(new Error(errorMessage));
      }
    );
  }

  // 创建游戏
  async createGame(config: GameConfig): Promise<ApiResponse<GameState>> {
    try {
      const response = await this.client.post('/games', config);
      return {
        success: true,
        data: response.data,
        message: '游戏创建成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建游戏失败'
      };
    }
  }

  // 加入游戏
  async joinGame(gameId: string): Promise<ApiResponse<GameState>> {
    try {
      const response = await this.client.post(`/games/${gameId}/join`);
      return {
        success: true,
        data: response.data,
        message: '成功加入游戏'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '加入游戏失败'
      };
    }
  }

  // 获取游戏状态
  async getGameState(gameId: string): Promise<ApiResponse<GameState>> {
    try {
      const response = await this.client.get(`/games/${gameId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取游戏状态失败'
      };
    }
  }

  // 开始游戏
  async startGame(gameId: string): Promise<ApiResponse<GameState>> {
    try {
      const response = await this.client.post(`/games/${gameId}/start`);
      return {
        success: true,
        data: response.data,
        message: '游戏已开始'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '开始游戏失败'
      };
    }
  }

  // 投票
  async submitVote(gameId: string, vote: Vote): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.post(`/games/${gameId}/vote`, vote);
      return {
        success: true,
        data: response.data,
        message: '投票提交成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '投票失败'
      };
    }
  }

  // 执行行动
  async submitAction(gameId: string, action: Action): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.post(`/games/${gameId}/action`, action);
      return {
        success: true,
        data: response.data,
        message: '行动执行成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '行动执行失败'
      };
    }
  }

  // 发送聊天消息
  async sendChatMessage(gameId: string, message: string): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.post(`/games/${gameId}/chat`, { message });
      return {
        success: true,
        data: response.data,
        message: '消息发送成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '消息发送失败'
      };
    }
  }

  // 获取游戏历史
  async getGameHistory(gameId: string): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.get(`/games/${gameId}/history`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取游戏历史失败'
      };
    }
  }

  // 健康检查
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      const response = await this.client.get('/health');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '服务器连接失败'
      };
    }
  }
}

// 导出API实例
export const gameApi = new GameAPI();