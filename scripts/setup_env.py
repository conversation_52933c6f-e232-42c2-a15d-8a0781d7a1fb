#!/usr/bin/env python3
"""
环境变量设置脚本
支持变量替换和自动一致性配置
"""
import os
import sys
import re
from pathlib import Path


def load_env_with_substitution(env_file: str = ".env"):
    """加载环境变量文件并支持变量替换"""
    env_path = Path(env_file)
    
    if not env_path.exists():
        print(f"环境变量文件不存在: {env_file}")
        return {}
    
    env_vars = {}
    
    # 第一遍：加载所有变量
    with open(env_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                continue
            
            # 解析 KEY=VALUE
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 移除引号
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]
                
                env_vars[key] = value
    
    # 第二遍：处理变量替换
    max_iterations = 10  # 防止无限循环
    for iteration in range(max_iterations):
        changed = False
        
        for key, value in env_vars.items():
            # 查找 ${VAR} 或 $VAR 模式
            original_value = value
            
            # 替换 ${VAR} 格式
            def replace_braced(match):
                var_name = match.group(1)
                return env_vars.get(var_name, os.environ.get(var_name, match.group(0)))
            
            value = re.sub(r'\$\{([^}]+)\}', replace_braced, value)
            
            # 替换 $VAR 格式（简单变量名）
            def replace_simple(match):
                var_name = match.group(1)
                return env_vars.get(var_name, os.environ.get(var_name, match.group(0)))
            
            value = re.sub(r'\$([A-Za-z_][A-Za-z0-9_]*)', replace_simple, value)
            
            if value != original_value:
                env_vars[key] = value
                changed = True
        
        if not changed:
            break
    
    return env_vars


def setup_consistent_config(existing_vars: dict = None):
    """设置一致性配置"""
    if existing_vars is None:
        existing_vars = {}

    env_vars = {}

    # 获取后端端口（优先从已加载的变量中获取）
    backend_port = existing_vars.get('BACKEND_PORT', os.environ.get('BACKEND_PORT', '8000'))

    # 只有在没有明确设置API URL时才自动生成
    if 'REACT_APP_API_URL' not in existing_vars or '${' in existing_vars.get('REACT_APP_API_URL', ''):
        # 如果API URL包含变量替换，说明已经处理过了，不需要重新设置
        pass
    else:
        # 自动设置前端连接地址（仅作为备用）
        if 'REACT_APP_API_URL' not in existing_vars:
            env_vars['REACT_APP_API_URL'] = f"http://localhost:{backend_port}/api"
        if 'REACT_APP_WEBSOCKET_URL' not in existing_vars:
            env_vars['REACT_APP_WEBSOCKET_URL'] = f"http://localhost:{backend_port}"

    return env_vars


def apply_environment_variables(env_vars: dict):
    """应用环境变量到当前进程"""
    for key, value in env_vars.items():
        os.environ[key] = str(value)
        print(f"设置环境变量: {key}={value}")


def create_enhanced_env_template():
    """创建支持变量替换的环境变量模板"""
    template_content = """# 狼人杀AI游戏环境变量配置（支持变量替换）
# 复制此文件为 .env 并根据需要修改配置

# ==================== 基础配置 ====================

# 运行环境
WOLFKILL_ENV=development
NODE_ENV=development
FLASK_ENV=development

# ==================== 服务端口配置 ====================

# 后端服务端口
BACKEND_PORT=8000

# 前端服务端口
FRONTEND_PORT=3000

# ==================== 自动配置（支持变量替换）====================

# 前端API连接地址（自动使用后端端口）
REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}

# 其他配置也可以使用变量
DATABASE_URL=postgresql://wolfkill_user:secure_password@localhost:${POSTGRES_PORT:-5432}/wolfkill

# ==================== LLM API配置 ====================

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地LLM配置
QWEN3_BASE_URL=http://localhost:8005/v1
QWEN3_API_KEY=EMPTY

# ==================== 其他配置 ====================

# 数据库端口（带默认值）
POSTGRES_PORT=5432
REDIS_PORT=6379

# 监控端口
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# 调试配置
DEBUG=true
VERBOSE_LOGGING=true
"""
    
    with open('.env.template.enhanced', 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print("✅ 已创建增强版环境变量模板: .env.template.enhanced")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="环境变量设置脚本")
    parser.add_argument("--env-file", default=".env", help="环境变量文件路径")
    parser.add_argument("--create-template", action="store_true", help="创建增强版模板")
    parser.add_argument("--show-vars", action="store_true", help="显示处理后的环境变量")
    parser.add_argument("--apply", action="store_true", help="应用环境变量到当前进程")
    
    args = parser.parse_args()
    
    if args.create_template:
        create_enhanced_env_template()
        return
    
    # 加载环境变量文件
    if Path(args.env_file).exists():
        print(f"📋 加载环境变量文件: {args.env_file}")
        env_vars = load_env_with_substitution(args.env_file)
    else:
        print(f"⚠️  环境变量文件不存在: {args.env_file}")
        env_vars = {}
    
    # 设置一致性配置
    consistent_vars = setup_consistent_config(env_vars)
    env_vars.update(consistent_vars)
    
    if args.show_vars:
        print("\n📊 处理后的环境变量:")
        for key, value in sorted(env_vars.items()):
            # 隐藏敏感信息
            if any(sensitive in key.lower() for sensitive in ['key', 'password', 'secret']):
                display_value = "***隐藏***"
            else:
                display_value = value
            print(f"  {key}={display_value}")
    
    if args.apply:
        print("\n🔧 应用环境变量:")
        apply_environment_variables(env_vars)
    
    # 输出shell脚本格式（用于source）
    if not args.show_vars and not args.apply:
        print("# 环境变量设置脚本")
        print("# 使用方法: source <(python scripts/setup_env.py)")
        for key, value in env_vars.items():
            print(f'export {key}="{value}"')


if __name__ == "__main__":
    main()
