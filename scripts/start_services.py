#!/usr/bin/env python3
"""
服务启动脚本
使用配置文件管理前端和后端服务的启动
"""
import os
import sys
import subprocess
import time
import signal
import argparse
from pathlib import Path
from typing import List, Dict, Optional
import threading
import requests

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.services_config import (
    services_config_manager,
    get_backend_config,
    get_frontend_config,
    get_api_connection_config
)


class ServiceManager:
    """服务管理器"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start_backend(self) -> bool:
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        try:
            # 获取后端配置
            backend_config = get_backend_config(self.environment)
            
            # 应用环境变量
            services_config_manager.apply_environment_variables("backend", self.environment)
            
            # 验证配置
            errors = services_config_manager.validate_service_config("backend", self.environment)
            if errors:
                print(f"❌ 后端配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
                return False
            
            # 启动后端进程
            backend_dir = project_root / "backend"
            backend_script = backend_dir / "api" / "app.py"
            
            if not backend_script.exists():
                print(f"❌ 后端脚本不存在: {backend_script}")
                return False
            
            # 设置环境变量
            env = os.environ.copy()
            env.update(services_config_manager.get_environment_variables("backend", self.environment))
            
            # 启动进程
            process = subprocess.Popen(
                [sys.executable, str(backend_script)],
                cwd=str(backend_dir),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes["backend"] = process
            
            # 启动日志监控线程
            threading.Thread(
                target=self._monitor_process_output,
                args=("backend", process),
                daemon=True
            ).start()
            
            # 等待服务启动
            if self._wait_for_service("backend", backend_config):
                print(f"✅ 后端服务已启动: {backend_config.get_url()}")
                return True
            else:
                print("❌ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动后端服务失败: {e}")
            return False
    
    def start_frontend(self) -> bool:
        """启动前端服务"""
        print("🌐 启动前端服务...")
        
        try:
            # 获取前端配置
            frontend_config = get_frontend_config(self.environment)
            api_config = get_api_connection_config(self.environment)
            
            # 验证配置
            errors = services_config_manager.validate_service_config("frontend", self.environment)
            if errors:
                print(f"❌ 前端配置验证失败:")
                for error in errors:
                    print(f"  - {error}")
                return False
            
            # 检查Node.js和npm
            if not self._check_node_environment():
                return False
            
            # 前端目录
            frontend_dir = project_root / "frontend"
            
            if not frontend_dir.exists():
                print(f"❌ 前端目录不存在: {frontend_dir}")
                return False
            
            # 检查依赖
            if not self._check_frontend_dependencies(frontend_dir):
                print("📦 安装前端依赖...")
                if not self._install_frontend_dependencies(frontend_dir):
                    return False
            
            # 设置环境变量
            env = os.environ.copy()
            env.update(services_config_manager.get_environment_variables("frontend", self.environment))
            
            # 设置React应用环境变量
            env["REACT_APP_API_URL"] = api_config["base_url"]
            env["REACT_APP_WEBSOCKET_URL"] = api_config["websocket_url"]
            env["PORT"] = str(frontend_config.port)
            
            # 启动前端进程
            process = subprocess.Popen(
                ["npm", "start"],
                cwd=str(frontend_dir),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes["frontend"] = process
            
            # 启动日志监控线程
            threading.Thread(
                target=self._monitor_process_output,
                args=("frontend", process),
                daemon=True
            ).start()
            
            # 等待服务启动
            if self._wait_for_service("frontend", frontend_config):
                print(f"✅ 前端服务已启动: {frontend_config.get_url()}")
                return True
            else:
                print("❌ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动前端服务失败: {e}")
            return False
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        print(f"🎮 启动狼人杀AI游戏服务 (环境: {self.environment})")
        print("=" * 60)
        
        self.running = True
        
        # 获取启动顺序
        startup_order = services_config_manager.get_config_manager().get_config(
            "services_config", 
            f"{self.environment}.startup_order", 
            ["backend", "frontend"]
        )
        
        success = True
        
        for service in startup_order:
            if service == "backend":
                if not self.start_backend():
                    success = False
                    break
            elif service == "frontend":
                if not self.start_frontend():
                    success = False
                    break
            
            # 服务间启动间隔
            time.sleep(2)
        
        if success:
            print("\n🎉 所有服务启动成功!")
            self._print_service_info()
            return True
        else:
            print("\n❌ 服务启动失败")
            self.stop_all_services()
            return False
    
    def stop_all_services(self):
        """停止所有服务"""
        print("\n🛑 停止所有服务...")
        
        self.running = False
        
        for service_name, process in self.processes.items():
            try:
                print(f"  停止 {service_name}...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"  强制终止 {service_name}...")
                    process.kill()
                    process.wait()
                
                print(f"  ✅ {service_name} 已停止")
                
            except Exception as e:
                print(f"  ❌ 停止 {service_name} 失败: {e}")
        
        self.processes.clear()
        print("👋 所有服务已停止")
    
    def _wait_for_service(self, service_name: str, config, timeout: int = 30) -> bool:
        """等待服务启动"""
        print(f"  等待 {service_name} 服务启动...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                if service_name == "backend":
                    # 检查后端健康状态
                    response = requests.get(f"{config.get_url()}/api/health", timeout=2)
                    if response.status_code == 200:
                        return True
                elif service_name == "frontend":
                    # 检查前端是否响应
                    response = requests.get(config.get_url(), timeout=2)
                    if response.status_code == 200:
                        return True
            except requests.RequestException:
                pass
            
            time.sleep(1)
        
        return False
    
    def _monitor_process_output(self, service_name: str, process: subprocess.Popen):
        """监控进程输出"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line and self.running:
                    print(f"[{service_name}] {line.rstrip()}")
        except Exception:
            pass
    
    def _check_node_environment(self) -> bool:
        """检查Node.js环境"""
        try:
            # 检查Node.js
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Node.js 未安装")
                return False
            
            node_version = result.stdout.strip()
            print(f"✅ Node.js 版本: {node_version}")
            
            # 检查npm
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ npm 未安装")
                return False
            
            npm_version = result.stdout.strip()
            print(f"✅ npm 版本: {npm_version}")
            
            return True
            
        except FileNotFoundError:
            print("❌ Node.js 或 npm 未找到，请先安装 Node.js")
            return False
    
    def _check_frontend_dependencies(self, frontend_dir: Path) -> bool:
        """检查前端依赖"""
        node_modules = frontend_dir / "node_modules"
        package_json = frontend_dir / "package.json"
        
        return node_modules.exists() and package_json.exists()
    
    def _install_frontend_dependencies(self, frontend_dir: Path) -> bool:
        """安装前端依赖"""
        try:
            result = subprocess.run(
                ["npm", "install"],
                cwd=str(frontend_dir),
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ 前端依赖安装成功")
                return True
            else:
                print(f"❌ 前端依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 安装前端依赖时出错: {e}")
            return False
    
    def _print_service_info(self):
        """打印服务信息"""
        print("\n📋 服务信息:")
        print("-" * 40)
        
        backend_config = get_backend_config(self.environment)
        frontend_config = get_frontend_config(self.environment)
        
        print(f"🔧 后端服务: {backend_config.get_url()}")
        print(f"   - API文档: {backend_config.get_url()}/api/health")
        print(f"   - WebSocket: ws://{backend_config.host}:{backend_config.port}")
        
        print(f"🌐 前端应用: {frontend_config.get_url()}")
        print(f"   - 用户界面: {frontend_config.get_url()}")
        
        print(f"\n🌍 环境: {self.environment}")
        print("\n按 Ctrl+C 停止所有服务")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def wait_for_shutdown(self):
        """等待关闭信号"""
        try:
            while self.running and any(p.poll() is None for p in self.processes.values()):
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_all_services()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="狼人杀AI游戏服务管理器")
    parser.add_argument(
        "--env", 
        choices=["development", "testing", "production"],
        default="development",
        help="运行环境"
    )
    parser.add_argument(
        "--service",
        choices=["backend", "frontend", "all"],
        default="all",
        help="要启动的服务"
    )
    parser.add_argument(
        "--config-check",
        action="store_true",
        help="仅检查配置，不启动服务"
    )
    
    args = parser.parse_args()
    
    # 创建服务管理器
    service_manager = ServiceManager(args.env)
    
    if args.config_check:
        # 仅检查配置
        print("🔍 检查服务配置...")
        
        backend_errors = services_config_manager.validate_service_config("backend", args.env)
        frontend_errors = services_config_manager.validate_service_config("frontend", args.env)
        
        if backend_errors:
            print("❌ 后端配置错误:")
            for error in backend_errors:
                print(f"  - {error}")
        else:
            print("✅ 后端配置正确")
        
        if frontend_errors:
            print("❌ 前端配置错误:")
            for error in frontend_errors:
                print(f"  - {error}")
        else:
            print("✅ 前端配置正确")
        
        return
    
    # 启动服务
    try:
        if args.service == "backend":
            if service_manager.start_backend():
                service_manager.wait_for_shutdown()
        elif args.service == "frontend":
            if service_manager.start_frontend():
                service_manager.wait_for_shutdown()
        else:  # all
            if service_manager.start_all_services():
                service_manager.wait_for_shutdown()
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
