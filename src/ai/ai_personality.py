"""
AI个性化和难度系统
实现多种AI难度等级和个性化参数系统
"""
import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

from ..models.enums import Role
from ..config.config_manager import get_config


class DifficultyLevel(Enum):
    """AI难度等级"""
    BEGINNER = "beginner"      # 新手：简单策略，较多随机性
    EASY = "easy"              # 简单：基础策略，一些随机性
    NORMAL = "normal"          # 普通：平衡策略，适度随机性
    HARD = "hard"              # 困难：高级策略，少量随机性
    EXPERT = "expert"          # 专家：最优策略，极少随机性
    MASTER = "master"          # 大师：完美策略，几乎无随机性


class PersonalityType(Enum):
    """个性类型"""
    AGGRESSIVE = "aggressive"      # 攻击型：主动出击，高风险高回报
    DEFENSIVE = "defensive"        # 防守型：谨慎保守，重视生存
    ANALYTICAL = "analytical"      # 分析型：逻辑推理，数据驱动
    INTUITIVE = "intuitive"        # 直觉型：凭感觉，快速决策
    SOCIAL = "social"              # 社交型：重视团队，善于沟通
    DECEPTIVE = "deceptive"        # 欺骗型：善于伪装，误导他人
    LOYAL = "loyal"                # 忠诚型：团队至上，可靠稳定
    CHAOTIC = "chaotic"            # 混乱型：不可预测，随机行为


@dataclass
class PersonalityTraits:
    """个性特征"""
    # 基础特征 (0.0-1.0)
    aggressiveness: float = 0.5     # 攻击性
    cautiousness: float = 0.5       # 谨慎性
    analytical_thinking: float = 0.5 # 分析思维
    intuition: float = 0.5          # 直觉
    social_skills: float = 0.5      # 社交技能
    deception_ability: float = 0.5  # 欺骗能力
    loyalty: float = 0.5            # 忠诚度
    unpredictability: float = 0.5   # 不可预测性
    
    # 决策特征
    risk_tolerance: float = 0.5     # 风险容忍度
    patience: float = 0.5           # 耐心
    adaptability: float = 0.5       # 适应性
    confidence: float = 0.5         # 自信心
    
    # 沟通特征
    verbosity: float = 0.5          # 话多程度
    persuasiveness: float = 0.5     # 说服力
    trustworthiness: float = 0.5    # 可信度
    emotional_stability: float = 0.5 # 情绪稳定性


@dataclass
class AIConfiguration:
    """AI配置"""
    difficulty: DifficultyLevel
    personality_type: PersonalityType
    traits: PersonalityTraits
    role_specific_params: Dict[Role, Dict[str, float]] = field(default_factory=dict)
    learning_enabled: bool = False
    adaptation_rate: float = 0.1
    memory_retention: float = 0.8
    
    # 行为调整参数
    speech_frequency: float = 0.5   # 发言频率
    vote_confidence: float = 0.7    # 投票信心
    information_sharing: float = 0.5 # 信息分享倾向
    alliance_tendency: float = 0.5   # 结盟倾向


class PersonalityGenerator:
    """个性生成器"""
    
    @staticmethod
    def generate_random_personality() -> PersonalityTraits:
        """生成随机个性"""
        return PersonalityTraits(
            aggressiveness=random.uniform(0.2, 0.8),
            cautiousness=random.uniform(0.2, 0.8),
            analytical_thinking=random.uniform(0.3, 0.9),
            intuition=random.uniform(0.2, 0.8),
            social_skills=random.uniform(0.3, 0.8),
            deception_ability=random.uniform(0.2, 0.7),
            loyalty=random.uniform(0.4, 0.9),
            unpredictability=random.uniform(0.1, 0.6),
            risk_tolerance=random.uniform(0.2, 0.8),
            patience=random.uniform(0.3, 0.8),
            adaptability=random.uniform(0.4, 0.9),
            confidence=random.uniform(0.3, 0.8),
            verbosity=random.uniform(0.2, 0.8),
            persuasiveness=random.uniform(0.3, 0.8),
            trustworthiness=random.uniform(0.4, 0.9),
            emotional_stability=random.uniform(0.3, 0.8)
        )
    
    @staticmethod
    def generate_personality_by_type(personality_type: PersonalityType) -> PersonalityTraits:
        """根据类型生成个性"""
        try:
            # 从配置文件获取个性特征
            personality_config = get_config("ai_config", f"personality_types.{personality_type.value}.traits")
            if personality_config:
                # 添加一些随机变化
                variation = 0.1
                traits_dict = {}

                for field in PersonalityTraits.__dataclass_fields__:
                    base_value = personality_config.get(field, 0.5)
                    # 添加随机变化，但保持在合理范围内
                    random_offset = random.uniform(-variation, variation)
                    new_value = max(0.0, min(1.0, base_value + random_offset))
                    traits_dict[field] = new_value

                return PersonalityTraits(**traits_dict)
        except Exception:
            pass

        # 回退到原有逻辑
        base_traits = PersonalityGenerator.generate_random_personality()

        if personality_type == PersonalityType.AGGRESSIVE:
            base_traits.aggressiveness = random.uniform(0.7, 0.9)
            base_traits.risk_tolerance = random.uniform(0.6, 0.9)
            base_traits.cautiousness = random.uniform(0.1, 0.4)
            base_traits.confidence = random.uniform(0.6, 0.9)

        elif personality_type == PersonalityType.DEFENSIVE:
            base_traits.cautiousness = random.uniform(0.7, 0.9)
            base_traits.aggressiveness = random.uniform(0.1, 0.4)
            base_traits.risk_tolerance = random.uniform(0.1, 0.4)
            base_traits.patience = random.uniform(0.6, 0.9)

        elif personality_type == PersonalityType.ANALYTICAL:
            base_traits.analytical_thinking = random.uniform(0.8, 1.0)
            base_traits.intuition = random.uniform(0.1, 0.3)
            base_traits.patience = random.uniform(0.7, 0.9)
            base_traits.emotional_stability = random.uniform(0.7, 0.9)

        elif personality_type == PersonalityType.INTUITIVE:
            base_traits.intuition = random.uniform(0.7, 0.9)
            base_traits.analytical_thinking = random.uniform(0.2, 0.5)
            base_traits.adaptability = random.uniform(0.6, 0.9)
            base_traits.confidence = random.uniform(0.6, 0.8)

        elif personality_type == PersonalityType.SOCIAL:
            base_traits.social_skills = random.uniform(0.8, 1.0)
            base_traits.persuasiveness = random.uniform(0.7, 0.9)
            base_traits.trustworthiness = random.uniform(0.6, 0.9)
            base_traits.verbosity = random.uniform(0.6, 0.9)

        elif personality_type == PersonalityType.DECEPTIVE:
            base_traits.deception_ability = random.uniform(0.7, 0.9)
            base_traits.trustworthiness = random.uniform(0.1, 0.4)
            base_traits.social_skills = random.uniform(0.5, 0.8)
            base_traits.unpredictability = random.uniform(0.4, 0.7)

        elif personality_type == PersonalityType.LOYAL:
            base_traits.loyalty = random.uniform(0.8, 1.0)
            base_traits.trustworthiness = random.uniform(0.7, 0.9)
            base_traits.emotional_stability = random.uniform(0.6, 0.9)
            base_traits.deception_ability = random.uniform(0.1, 0.3)

        elif personality_type == PersonalityType.CHAOTIC:
            base_traits.unpredictability = random.uniform(0.7, 0.9)
            base_traits.emotional_stability = random.uniform(0.1, 0.4)
            base_traits.adaptability = random.uniform(0.6, 0.9)
            base_traits.patience = random.uniform(0.1, 0.4)

        return base_traits


class DifficultyManager:
    """难度管理器"""
    
    @staticmethod
    def get_difficulty_parameters(difficulty: DifficultyLevel) -> Dict[str, float]:
        """获取难度参数"""
        try:
            # 从配置文件获取难度参数
            difficulty_config = get_config("ai_config", f"difficulty_levels.{difficulty.value}.parameters")
            if difficulty_config:
                return difficulty_config
        except Exception:
            pass

        # 回退到硬编码参数
        params = {
            DifficultyLevel.BEGINNER: {
                "decision_accuracy": 0.3,
                "strategic_thinking": 0.2,
                "memory_retention": 0.4,
                "pattern_recognition": 0.2,
                "random_factor": 0.5,
                "reaction_time": 0.3,
                "information_processing": 0.3,
                "long_term_planning": 0.2
            },
            DifficultyLevel.EASY: {
                "decision_accuracy": 0.5,
                "strategic_thinking": 0.4,
                "memory_retention": 0.6,
                "pattern_recognition": 0.4,
                "random_factor": 0.3,
                "reaction_time": 0.5,
                "information_processing": 0.5,
                "long_term_planning": 0.4
            },
            DifficultyLevel.NORMAL: {
                "decision_accuracy": 0.7,
                "strategic_thinking": 0.6,
                "memory_retention": 0.7,
                "pattern_recognition": 0.6,
                "random_factor": 0.2,
                "reaction_time": 0.7,
                "information_processing": 0.7,
                "long_term_planning": 0.6
            },
            DifficultyLevel.HARD: {
                "decision_accuracy": 0.8,
                "strategic_thinking": 0.8,
                "memory_retention": 0.9,
                "pattern_recognition": 0.8,
                "random_factor": 0.1,
                "reaction_time": 0.8,
                "information_processing": 0.8,
                "long_term_planning": 0.8
            },
            DifficultyLevel.EXPERT: {
                "decision_accuracy": 0.9,
                "strategic_thinking": 0.9,
                "memory_retention": 0.95,
                "pattern_recognition": 0.9,
                "random_factor": 0.05,
                "reaction_time": 0.9,
                "information_processing": 0.9,
                "long_term_planning": 0.9
            },
            DifficultyLevel.MASTER: {
                "decision_accuracy": 0.95,
                "strategic_thinking": 0.95,
                "memory_retention": 0.98,
                "pattern_recognition": 0.95,
                "random_factor": 0.02,
                "reaction_time": 0.95,
                "information_processing": 0.95,
                "long_term_planning": 0.95
            }
        }
        
        return params.get(difficulty, params[DifficultyLevel.NORMAL])


class AIPersonalityManager:
    """AI个性管理器"""
    
    def __init__(self):
        self.configurations = {}
        self.personality_templates = self._create_personality_templates()
    
    def _create_personality_templates(self) -> Dict[str, AIConfiguration]:
        """创建个性模板"""
        templates = {}
        
        # 创建各种预设个性
        for personality_type in PersonalityType:
            for difficulty in DifficultyLevel:
                template_name = f"{personality_type.value}_{difficulty.value}"
                
                traits = PersonalityGenerator.generate_personality_by_type(personality_type)
                
                config = AIConfiguration(
                    difficulty=difficulty,
                    personality_type=personality_type,
                    traits=traits
                )
                
                # 根据难度调整配置
                difficulty_params = DifficultyManager.get_difficulty_parameters(difficulty)
                config.vote_confidence = difficulty_params["decision_accuracy"]
                config.adaptation_rate = difficulty_params["strategic_thinking"] * 0.2
                config.memory_retention = difficulty_params["memory_retention"]
                
                templates[template_name] = config
        
        return templates
    
    def create_ai_configuration(self, difficulty: DifficultyLevel = DifficultyLevel.NORMAL,
                              personality_type: Optional[PersonalityType] = None,
                              custom_traits: Optional[PersonalityTraits] = None) -> AIConfiguration:
        """创建AI配置"""
        
        if personality_type is None:
            personality_type = random.choice(list(PersonalityType))
        
        if custom_traits is None:
            traits = PersonalityGenerator.generate_personality_by_type(personality_type)
        else:
            traits = custom_traits
        
        config = AIConfiguration(
            difficulty=difficulty,
            personality_type=personality_type,
            traits=traits
        )
        
        # 应用难度参数
        difficulty_params = DifficultyManager.get_difficulty_parameters(difficulty)
        config.vote_confidence = difficulty_params["decision_accuracy"]
        config.adaptation_rate = difficulty_params["strategic_thinking"] * 0.2
        config.memory_retention = difficulty_params["memory_retention"]
        
        # 根据个性调整行为参数
        self._adjust_behavior_parameters(config)
        
        return config
    
    def _adjust_behavior_parameters(self, config: AIConfiguration):
        """根据个性调整行为参数"""
        traits = config.traits
        
        # 调整发言频率
        config.speech_frequency = (traits.verbosity + traits.social_skills) / 2
        
        # 调整信息分享倾向
        config.information_sharing = (traits.trustworthiness + traits.social_skills - traits.deception_ability) / 2
        
        # 调整结盟倾向
        config.alliance_tendency = (traits.social_skills + traits.loyalty) / 2
        
        # 确保参数在合理范围内
        config.speech_frequency = max(0.1, min(0.9, config.speech_frequency))
        config.information_sharing = max(0.1, min(0.9, config.information_sharing))
        config.alliance_tendency = max(0.1, min(0.9, config.alliance_tendency))
    
    def get_template(self, template_name: str) -> Optional[AIConfiguration]:
        """获取个性模板"""
        return self.personality_templates.get(template_name)
    
    def list_templates(self) -> List[str]:
        """列出所有模板"""
        return list(self.personality_templates.keys())
    
    def register_configuration(self, player_id: int, config: AIConfiguration):
        """注册玩家配置"""
        self.configurations[player_id] = config
    
    def get_configuration(self, player_id: int) -> Optional[AIConfiguration]:
        """获取玩家配置"""
        return self.configurations.get(player_id)
    
    def create_balanced_team(self, team_size: int, difficulty: DifficultyLevel) -> List[AIConfiguration]:
        """创建平衡的AI团队"""
        configs = []
        personality_types = list(PersonalityType)
        
        for i in range(team_size):
            # 确保个性类型多样化
            personality_type = personality_types[i % len(personality_types)]
            
            config = self.create_ai_configuration(difficulty, personality_type)
            configs.append(config)
        
        return configs
    
    def analyze_team_composition(self, configs: List[AIConfiguration]) -> Dict[str, Any]:
        """分析团队组成"""
        analysis = {
            "difficulty_distribution": {},
            "personality_distribution": {},
            "average_traits": {},
            "team_balance_score": 0.0
        }
        
        # 统计难度分布
        for config in configs:
            difficulty = config.difficulty.value
            analysis["difficulty_distribution"][difficulty] = analysis["difficulty_distribution"].get(difficulty, 0) + 1
        
        # 统计个性分布
        for config in configs:
            personality = config.personality_type.value
            analysis["personality_distribution"][personality] = analysis["personality_distribution"].get(personality, 0) + 1
        
        # 计算平均特征
        if configs:
            trait_sums = {}
            for config in configs:
                traits = config.traits
                for attr_name in dir(traits):
                    if not attr_name.startswith('_') and isinstance(getattr(traits, attr_name), float):
                        trait_sums[attr_name] = trait_sums.get(attr_name, 0) + getattr(traits, attr_name)
            
            for trait_name, total in trait_sums.items():
                analysis["average_traits"][trait_name] = total / len(configs)
        
        # 计算团队平衡分数（个性多样性）
        personality_count = len(set(config.personality_type for config in configs))
        max_diversity = min(len(configs), len(PersonalityType))
        analysis["team_balance_score"] = personality_count / max_diversity if max_diversity > 0 else 0
        
        return analysis
