"""
统一配置管理系统
支持YAML/JSON配置文件，环境变量覆盖，配置验证和热重载
"""
import os
import json
import yaml
import copy
from typing import Dict, Any, Optional, List, Union, Callable
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


@dataclass
class ConfigSchema:
    """配置模式定义"""
    name: str
    description: str
    required_fields: List[str] = field(default_factory=list)
    optional_fields: List[str] = field(default_factory=list)
    field_types: Dict[str, type] = field(default_factory=dict)
    validators: Dict[str, Callable] = field(default_factory=dict)
    default_values: Dict[str, Any] = field(default_factory=dict)


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变化监听器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_modified = {}
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = event.src_path
        if not file_path.endswith(('.yaml', '.yml', '.json')):
            return
        
        # 防止重复触发
        current_time = time.time()
        if file_path in self.last_modified:
            if current_time - self.last_modified[file_path] < 1.0:
                return
        
        self.last_modified[file_path] = current_time
        
        # 重新加载配置
        try:
            self.config_manager._reload_config_file(file_path)
            print(f"配置文件已重新加载: {file_path}")
        except Exception as e:
            print(f"重新加载配置文件失败 {file_path}: {e}")


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置存储
        self.configs: Dict[str, Dict[str, Any]] = {}
        self.schemas: Dict[str, ConfigSchema] = {}
        self.file_paths: Dict[str, Path] = {}
        
        # 热重载相关
        self.hot_reload_enabled = False
        self.observer = None
        self.file_handler = None
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 初始化默认配置模式
        self._initialize_default_schemas()
    
    def _initialize_default_schemas(self):
        """初始化默认配置模式"""
        # AI配置模式
        self.register_schema(ConfigSchema(
            name="ai_config",
            description="AI超参数配置",
            required_fields=["difficulty_levels", "personality_types"],
            optional_fields=["learning_enabled", "adaptation_settings"],
            field_types={
                "difficulty_levels": dict,
                "personality_types": dict,
                "learning_enabled": bool,
                "adaptation_settings": dict
            },
            default_values={
                "learning_enabled": False,
                "adaptation_settings": {"rate": 0.1, "memory_retention": 0.8}
            }
        ))
        
        # 游戏配置模式
        self.register_schema(ConfigSchema(
            name="game_config",
            description="游戏规则和模式配置",
            required_fields=["game_modes", "default_settings"],
            optional_fields=["time_limits", "special_rules"],
            field_types={
                "game_modes": dict,
                "default_settings": dict,
                "time_limits": dict,
                "special_rules": dict
            }
        ))
        
        # LLM配置模式
        self.register_schema(ConfigSchema(
            name="llm_config",
            description="大语言模型配置",
            required_fields=["providers"],
            optional_fields=["default_provider", "fallback_providers"],
            field_types={
                "providers": dict,
                "default_provider": str,
                "fallback_providers": list
            },
            default_values={
                "default_provider": "qwen3_30b",
                "fallback_providers": ["mock"]
            }
        ))

        # 服务配置模式
        self.register_schema(ConfigSchema(
            name="services_config",
            description="前端和后端服务配置",
            required_fields=["backend", "frontend"],
            optional_fields=["development", "production", "testing", "docker"],
            field_types={
                "backend": dict,
                "frontend": dict,
                "development": dict,
                "production": dict,
                "testing": dict,
                "docker": dict
            }
        ))
    
    def register_schema(self, schema: ConfigSchema):
        """注册配置模式"""
        with self._lock:
            self.schemas[schema.name] = schema
    
    def load_config(self, config_name: str, file_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """加载配置文件"""
        with self._lock:
            if file_path is None:
                # 自动查找配置文件
                file_path = self._find_config_file(config_name)
            else:
                file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
            # 加载配置内容
            config_data = self._load_file_content(file_path)
            
            # 应用环境变量覆盖
            config_data = self._apply_env_overrides(config_name, config_data)
            
            # 验证配置
            self._validate_config(config_name, config_data)
            
            # 存储配置
            self.configs[config_name] = config_data
            self.file_paths[config_name] = file_path
            
            return copy.deepcopy(config_data)
    
    def _find_config_file(self, config_name: str) -> Path:
        """查找配置文件"""
        possible_files = [
            self.config_dir / f"{config_name}.yaml",
            self.config_dir / f"{config_name}.yml",
            self.config_dir / f"{config_name}.json",
            Path(f"{config_name}.yaml"),
            Path(f"{config_name}.yml"),
            Path(f"{config_name}.json")
        ]
        
        for file_path in possible_files:
            if file_path.exists():
                return file_path
        
        raise FileNotFoundError(f"找不到配置文件: {config_name}")
    
    def _load_file_content(self, file_path: Path) -> Dict[str, Any]:
        """加载文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                elif file_path.suffix.lower() == '.json':
                    return json.load(f) or {}
                else:
                    raise ValueError(f"不支持的文件格式: {file_path.suffix}")
        except Exception as e:
            raise ValueError(f"加载配置文件失败 {file_path}: {e}")
    
    def _apply_env_overrides(self, config_name: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        env_prefix = f"WOLFKILL_{config_name.upper()}_"
        
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower()
                
                # 支持嵌套键，如 WOLFKILL_AI_CONFIG_DIFFICULTY_LEVELS_EASY
                keys = config_key.split('_')
                current = config_data
                
                # 导航到嵌套位置
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                
                # 设置值（尝试转换类型）
                final_key = keys[-1]
                current[final_key] = self._convert_env_value(value)
        
        return config_data
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值"""
        # 尝试转换为适当的类型
        if value.lower() in ['true', 'false']:
            return value.lower() == 'true'
        
        try:
            # 尝试转换为数字
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 尝试解析JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            pass
        
        return value
    
    def _validate_config(self, config_name: str, config_data: Dict[str, Any]):
        """验证配置"""
        if config_name not in self.schemas:
            return  # 没有模式定义，跳过验证
        
        schema = self.schemas[config_name]
        
        # 检查必需字段
        for field in schema.required_fields:
            if field not in config_data:
                raise ValueError(f"配置 {config_name} 缺少必需字段: {field}")
        
        # 检查字段类型
        for field, expected_type in schema.field_types.items():
            if field in config_data:
                value = config_data[field]
                if not isinstance(value, expected_type):
                    raise TypeError(f"配置 {config_name} 字段 {field} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}")
        
        # 运行自定义验证器
        for field, validator in schema.validators.items():
            if field in config_data:
                if not validator(config_data[field]):
                    raise ValueError(f"配置 {config_name} 字段 {field} 验证失败")
    
    def get_config(self, config_name: str, key: Optional[str] = None, default: Any = None) -> Any:
        """获取配置值"""
        with self._lock:
            if config_name not in self.configs:
                # 尝试自动加载
                try:
                    self.load_config(config_name)
                except Exception:
                    return default
            
            config = self.configs.get(config_name, {})
            
            if key is None:
                return copy.deepcopy(config)
            
            # 支持嵌套键，如 "difficulty_levels.easy.decision_accuracy"
            keys = key.split('.')
            current = config
            
            for k in keys:
                if isinstance(current, dict) and k in current:
                    current = current[k]
                else:
                    return default
            
            return copy.deepcopy(current)
    
    def set_config(self, config_name: str, key: str, value: Any, save: bool = True):
        """设置配置值"""
        with self._lock:
            if config_name not in self.configs:
                self.configs[config_name] = {}
            
            # 支持嵌套键
            keys = key.split('.')
            current = self.configs[config_name]
            
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            
            current[keys[-1]] = value
            
            if save:
                self.save_config(config_name)
    
    def save_config(self, config_name: str, file_path: Optional[Union[str, Path]] = None):
        """保存配置到文件"""
        with self._lock:
            if config_name not in self.configs:
                raise ValueError(f"配置不存在: {config_name}")
            
            if file_path is None:
                file_path = self.file_paths.get(config_name)
                if file_path is None:
                    file_path = self.config_dir / f"{config_name}.yaml"
            else:
                file_path = Path(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            config_data = self.configs[config_name]
            
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    if file_path.suffix.lower() in ['.yaml', '.yml']:
                        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)
                    elif file_path.suffix.lower() == '.json':
                        json.dump(config_data, f, indent=2, ensure_ascii=False)
                    else:
                        raise ValueError(f"不支持的文件格式: {file_path.suffix}")
                
                self.file_paths[config_name] = file_path
                
            except Exception as e:
                raise ValueError(f"保存配置文件失败 {file_path}: {e}")

    def enable_hot_reload(self):
        """启用热重载"""
        if self.hot_reload_enabled:
            return

        self.hot_reload_enabled = True
        self.file_handler = ConfigFileHandler(self)
        self.observer = Observer()

        # 监听配置目录
        if self.config_dir.exists():
            self.observer.schedule(self.file_handler, str(self.config_dir), recursive=True)

        # 监听已加载的配置文件
        for file_path in self.file_paths.values():
            if file_path.parent != self.config_dir:
                self.observer.schedule(self.file_handler, str(file_path.parent), recursive=False)

        self.observer.start()
        print("配置热重载已启用")

    def disable_hot_reload(self):
        """禁用热重载"""
        if not self.hot_reload_enabled:
            return

        self.hot_reload_enabled = False
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.observer = None
        self.file_handler = None
        print("配置热重载已禁用")

    def _reload_config_file(self, file_path: str):
        """重新加载配置文件"""
        file_path = Path(file_path)

        # 找到对应的配置名称
        config_name = None
        for name, path in self.file_paths.items():
            if path == file_path:
                config_name = name
                break

        if config_name:
            try:
                self.load_config(config_name, file_path)
                print(f"配置 {config_name} 已重新加载")
            except Exception as e:
                print(f"重新加载配置 {config_name} 失败: {e}")

    def list_configs(self) -> List[str]:
        """列出所有已加载的配置"""
        with self._lock:
            return list(self.configs.keys())

    def reload_config(self, config_name: str):
        """手动重新加载配置"""
        with self._lock:
            if config_name in self.file_paths:
                self.load_config(config_name, self.file_paths[config_name])
            else:
                self.load_config(config_name)

    def validate_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """验证所有配置"""
        results = {}

        with self._lock:
            for config_name in self.configs:
                try:
                    self._validate_config(config_name, self.configs[config_name])
                    results[config_name] = {"valid": True, "errors": []}
                except Exception as e:
                    results[config_name] = {"valid": False, "errors": [str(e)]}

        return results

    def create_default_config(self, config_name: str) -> Dict[str, Any]:
        """创建默认配置"""
        if config_name not in self.schemas:
            raise ValueError(f"未知的配置类型: {config_name}")

        schema = self.schemas[config_name]
        default_config = copy.deepcopy(schema.default_values)

        # 为必需字段创建空结构
        for field in schema.required_fields:
            if field not in default_config:
                field_type = schema.field_types.get(field, dict)
                if field_type == dict:
                    default_config[field] = {}
                elif field_type == list:
                    default_config[field] = []
                else:
                    default_config[field] = None

        return default_config

    def export_config(self, config_name: str, format: str = 'yaml') -> str:
        """导出配置为字符串"""
        with self._lock:
            if config_name not in self.configs:
                raise ValueError(f"配置不存在: {config_name}")

            config_data = self.configs[config_name]

            if format.lower() in ['yaml', 'yml']:
                return yaml.dump(config_data, default_flow_style=False, allow_unicode=True, indent=2)
            elif format.lower() == 'json':
                return json.dumps(config_data, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的格式: {format}")

    def import_config(self, config_name: str, config_str: str, format: str = 'yaml'):
        """从字符串导入配置"""
        with self._lock:
            try:
                if format.lower() in ['yaml', 'yml']:
                    config_data = yaml.safe_load(config_str) or {}
                elif format.lower() == 'json':
                    config_data = json.loads(config_str) or {}
                else:
                    raise ValueError(f"不支持的格式: {format}")

                # 验证配置
                self._validate_config(config_name, config_data)

                # 存储配置
                self.configs[config_name] = config_data

            except Exception as e:
                raise ValueError(f"导入配置失败: {e}")

    def get_config_info(self, config_name: str) -> Dict[str, Any]:
        """获取配置信息"""
        with self._lock:
            info = {
                "name": config_name,
                "loaded": config_name in self.configs,
                "file_path": str(self.file_paths.get(config_name, "未知")),
                "schema_available": config_name in self.schemas,
                "last_modified": None
            }

            if config_name in self.file_paths:
                file_path = self.file_paths[config_name]
                if file_path.exists():
                    stat = file_path.stat()
                    info["last_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()

            if config_name in self.schemas:
                schema = self.schemas[config_name]
                info["schema"] = {
                    "description": schema.description,
                    "required_fields": schema.required_fields,
                    "optional_fields": schema.optional_fields
                }

            return info

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.disable_hot_reload()


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config(config_name: str, key: Optional[str] = None, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get_config(config_name, key, default)


def set_config(config_name: str, key: str, value: Any, save: bool = True):
    """设置配置值的便捷函数"""
    config_manager.set_config(config_name, key, value, save)


def load_config(config_name: str, file_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
    """加载配置的便捷函数"""
    return config_manager.load_config(config_name, file_path)


def save_config(config_name: str, file_path: Optional[Union[str, Path]] = None):
    """保存配置的便捷函数"""
    config_manager.save_config(config_name, file_path)
