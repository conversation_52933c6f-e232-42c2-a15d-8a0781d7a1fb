"""
大模型配置管理
集中管理各种大模型的配置信息
"""
from typing import Dict, Optional
from ..ai.llm_integration import LLMConfig
from .config_manager import get_config


class LLMConfigManager:
    """大模型配置管理器"""
    
    def __init__(self):
        self.configs = self._initialize_configs()
    
    def _initialize_configs(self) -> Dict[str, LLMConfig]:
        """初始化配置"""
        return {
            # Qwen3-30B 本地服务配置
            "qwen3_30b": LLMConfig(
                provider="openai",
                model_name="ckpt/Qwen/Qwen3-30B-A3B",
                api_key="EMPTY",
                base_url="http://localhost:8005/v1",
                max_tokens=200,
                temperature=0.7,
                timeout=30
            ),
            
            # OpenAI GPT-3.5 配置
            "openai_gpt35": LLMConfig(
                provider="openai",
                model_name="gpt-3.5-turbo",
                max_tokens=150,
                temperature=0.7,
                timeout=30
            ),
            
            # OpenAI GPT-4 配置
            "openai_gpt4": LLMConfig(
                provider="openai",
                model_name="gpt-4",
                max_tokens=200,
                temperature=0.6,
                timeout=30
            ),
            
            # 模拟LLM配置（用于测试）
            "mock": LLMConfig(
                provider="mock",
                model_name="mock-model",
                max_tokens=100,
                temperature=0.7
            )
        }
    
    def get_config(self, config_name: str) -> Optional[LLMConfig]:
        """获取配置"""
        return self.configs.get(config_name)
    
    def add_config(self, name: str, config: LLMConfig):
        """添加配置"""
        self.configs[name] = config
    
    def update_qwen3_config(self, base_url: str = None, api_key: str = None, 
                           model_name: str = None, **kwargs):
        """更新Qwen3-30B配置"""
        if "qwen3_30b" not in self.configs:
            return False
        
        config = self.configs["qwen3_30b"]
        
        if base_url:
            config.base_url = base_url
        if api_key:
            config.api_key = api_key
        if model_name:
            config.model_name = model_name
        
        # 更新其他参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return True
    
    def get_default_config_name(self) -> str:
        """获取默认配置名称"""
        return "qwen3_30b"
    
    def list_available_configs(self) -> list:
        """列出可用配置"""
        return list(self.configs.keys())
    
    def validate_config(self, config_name: str) -> Dict[str, any]:
        """验证配置"""
        config = self.get_config(config_name)
        if not config:
            return {"valid": False, "error": "配置不存在"}
        
        validation_result = {
            "valid": True,
            "config_name": config_name,
            "provider": config.provider,
            "model_name": config.model_name,
            "has_api_key": bool(config.api_key),
            "has_base_url": bool(config.base_url),
            "warnings": []
        }
        
        # 检查必要参数
        if config.provider == "openai" and not config.api_key and config_name != "qwen3_30b":
            validation_result["warnings"].append("OpenAI配置缺少API密钥")
        
        if config.provider == "openai" and config.base_url and not config.base_url.startswith("http"):
            validation_result["warnings"].append("base_url格式可能不正确")
        
        return validation_result


# 全局配置管理器实例
llm_config_manager = LLMConfigManager()


def get_llm_config(config_name: str = None) -> LLMConfig:
    """获取LLM配置"""
    try:
        # 尝试从新配置系统获取
        if config_name is None:
            config_name = get_config("llm_config", "default_provider", "qwen3_30b")

        provider_config = get_config("llm_config", f"providers.{config_name}")
        if provider_config:
            return LLMConfig(
                provider=provider_config.get("provider", "openai"),
                model_name=provider_config.get("model_name", ""),
                api_key=provider_config.get("api_key", ""),
                base_url=provider_config.get("base_url"),
                max_tokens=provider_config.get("generation_params", {}).get("max_tokens", 200),
                temperature=provider_config.get("generation_params", {}).get("temperature", 0.7),
                timeout=provider_config.get("connection_params", {}).get("timeout", 30)
            )
    except Exception:
        pass

    # 回退到原有配置管理器
    if config_name is None:
        config_name = llm_config_manager.get_default_config_name()

    config = llm_config_manager.get_config(config_name)
    if config is None:
        # 如果指定配置不存在，返回默认配置
        config = llm_config_manager.get_config("qwen3_30b")

    return config


def update_qwen3_service_config(base_url: str = "http://localhost:8005/v1", 
                               api_key: str = "EMPTY",
                               model_name: str = "ckpt/Qwen/Qwen3-30B-A3B"):
    """更新Qwen3服务配置"""
    return llm_config_manager.update_qwen3_config(
        base_url=base_url,
        api_key=api_key,
        model_name=model_name
    )


def validate_llm_service(config_name: str = "qwen3_30b") -> bool:
    """验证LLM服务是否可用"""
    try:
        from ..ai.llm_integration import create_llm_manager
        
        llm_manager = create_llm_manager(config_name)
        
        # 尝试生成一个简单的测试文本
        test_prompt = "请说'测试成功'"
        response = llm_manager.provider.generate_text_sync(
            prompt=test_prompt,
            system="你是一个测试助手，请按要求回复。"
        )
        
        return len(response) > 0
    
    except Exception as e:
        print(f"LLM服务验证失败: {e}")
        return False


def get_service_status() -> Dict[str, any]:
    """获取服务状态"""
    status = {
        "qwen3_30b": {"available": False, "error": None},
        "mock": {"available": True, "error": None}
    }
    
    # 检查Qwen3-30B服务
    try:
        if validate_llm_service("qwen3_30b"):
            status["qwen3_30b"]["available"] = True
        else:
            status["qwen3_30b"]["error"] = "服务不可用或响应异常"
    except Exception as e:
        status["qwen3_30b"]["error"] = str(e)
    
    return status
