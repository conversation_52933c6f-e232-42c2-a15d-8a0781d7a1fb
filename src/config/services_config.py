"""
服务配置管理器
管理前端和后端服务的配置参数
"""
import os
import sys
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass

from .config_manager import get_config, ConfigManager


@dataclass
class ServiceConfig:
    """服务配置数据类"""
    name: str
    host: str
    port: int
    debug: bool = False
    environment: str = "development"
    
    def get_url(self, protocol: str = "http") -> str:
        """获取服务URL"""
        return f"{protocol}://{self.host}:{self.port}"


class ServicesConfigManager:
    """服务配置管理器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self._load_services_config()
    
    def _load_services_config(self):
        """加载服务配置"""
        try:
            self.config_manager.load_config("services_config")
        except Exception as e:
            print(f"警告: 无法加载服务配置文件: {e}")
            print("将使用默认配置")
    
    def get_backend_config(self, environment: str = None) -> ServiceConfig:
        """获取后端服务配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            # 获取基础后端配置
            backend_config = get_config("services_config", "backend", {})
            server_config = backend_config.get("server", {})
            
            # 获取环境特定配置
            env_config = get_config("services_config", f"{environment}.backend", {})
            
            # 合并配置
            host = env_config.get("host", server_config.get("host", "localhost"))
            port = env_config.get("port", server_config.get("port", 8000))
            debug = env_config.get("debug", server_config.get("debug", True))
            
            return ServiceConfig(
                name=backend_config.get("name", "Backend Service"),
                host=host,
                port=port,
                debug=debug,
                environment=environment
            )
        except Exception:
            # 返回默认配置
            return ServiceConfig(
                name="Backend Service",
                host="localhost",
                port=8000,
                debug=True,
                environment=environment
            )
    
    def get_frontend_config(self, environment: str = None) -> ServiceConfig:
        """获取前端服务配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            # 获取基础前端配置
            frontend_config = get_config("services_config", "frontend", {})
            dev_server_config = frontend_config.get("dev_server", {})
            
            # 获取环境特定配置
            env_config = get_config("services_config", f"{environment}.frontend", {})
            
            # 合并配置
            host = env_config.get("host", dev_server_config.get("host", "localhost"))
            port = env_config.get("port", dev_server_config.get("port", 3000))
            debug = environment == "development"
            
            return ServiceConfig(
                name=frontend_config.get("name", "Frontend Service"),
                host=host,
                port=port,
                debug=debug,
                environment=environment
            )
        except Exception:
            # 返回默认配置
            return ServiceConfig(
                name="Frontend Service",
                host="localhost",
                port=3000,
                debug=True,
                environment=environment
            )
    
    def get_api_connection_config(self, environment: str = None) -> Dict[str, Any]:
        """获取API连接配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            # 获取前端API连接配置
            api_config = get_config("services_config", "frontend.api_connection", {})
            
            # 获取后端配置以构建URL
            backend_config = self.get_backend_config(environment)
            
            # 构建API URL
            base_url = api_config.get("base_url", f"http://{backend_config.host}:{backend_config.port}/api")
            websocket_url = api_config.get("websocket_url", f"http://{backend_config.host}:{backend_config.port}")
            
            return {
                "base_url": base_url,
                "websocket_url": websocket_url,
                "timeout": api_config.get("timeout", 10000),
                "retry_attempts": api_config.get("retry_attempts", 3),
                "retry_delay": api_config.get("retry_delay", 1000)
            }
        except Exception:
            # 返回默认配置
            return {
                "base_url": "http://localhost:8000/api",
                "websocket_url": "http://localhost:8000",
                "timeout": 10000,
                "retry_attempts": 3,
                "retry_delay": 1000
            }
    
    def get_environment_variables(self, service: str, environment: str = None) -> Dict[str, str]:
        """获取服务的环境变量"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            # 获取服务特定的环境变量
            service_env_vars = get_config("services_config", f"{service}.environment_variables", {})
            
            # 获取环境特定的环境变量
            env_vars = get_config("services_config", f"{environment}.environment_variables", {})
            
            # 合并环境变量
            combined_vars = {**service_env_vars, **env_vars}
            
            # 处理变量替换
            processed_vars = {}
            for key, value in combined_vars.items():
                if isinstance(value, str):
                    # 替换项目根目录变量
                    value = value.replace("${PROJECT_ROOT}", str(Path(__file__).parent.parent.parent))
                processed_vars[key] = str(value)
            
            return processed_vars
        except Exception:
            return {}
    
    def get_cors_config(self, environment: str = None) -> Dict[str, Any]:
        """获取CORS配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            cors_config = get_config("services_config", "backend.cors", {})
            
            # 根据环境调整CORS配置
            if environment == "production":
                # 生产环境使用更严格的CORS配置
                prod_origins = get_config("services_config", "production.cors.origins", [])
                if prod_origins:
                    cors_config["origins"] = prod_origins
            
            return cors_config
        except Exception:
            # 返回默认CORS配置
            return {
                "origins": ["http://localhost:3000"],
                "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                "allow_headers": ["Content-Type", "Authorization"],
                "supports_credentials": True
            }
    
    def get_websocket_config(self, environment: str = None) -> Dict[str, Any]:
        """获取WebSocket配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            ws_config = get_config("services_config", "backend.websocket", {})
            
            # 根据环境调整WebSocket配置
            if environment == "production":
                ws_config["async_mode"] = "gevent"  # 生产环境使用gevent
            
            return ws_config
        except Exception:
            # 返回默认WebSocket配置
            return {
                "cors_allowed_origins": ["http://localhost:3000"],
                "async_mode": "threading",
                "ping_timeout": 60,
                "ping_interval": 25
            }
    
    def get_logging_config(self, service: str, environment: str = None) -> Dict[str, Any]:
        """获取日志配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            # 获取服务特定的日志配置
            logging_config = get_config("services_config", f"{service}.logging", {})
            
            # 获取环境特定的日志配置
            env_logging = get_config("services_config", f"{environment}.logging", {})
            
            # 合并配置
            config = {**logging_config, **env_logging}
            
            # 确保日志目录存在
            if "file_path" in config:
                log_file = Path(config["file_path"])
                log_file.parent.mkdir(parents=True, exist_ok=True)
            
            return config
        except Exception:
            # 返回默认日志配置
            return {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "file_path": f"logs/{service}.log"
            }
    
    def get_health_check_config(self, environment: str = None) -> Dict[str, Any]:
        """获取健康检查配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        try:
            health_config = get_config("services_config", f"{environment}.health_checks", {})
            return health_config
        except Exception:
            # 返回默认健康检查配置
            backend_config = self.get_backend_config(environment)
            frontend_config = self.get_frontend_config(environment)
            
            return {
                "backend": {
                    "url": f"http://{backend_config.host}:{backend_config.port}/api/health",
                    "interval": 30,
                    "timeout": 5,
                    "retries": 3
                },
                "frontend": {
                    "url": f"http://{frontend_config.host}:{frontend_config.port}",
                    "interval": 30,
                    "timeout": 5,
                    "retries": 3
                }
            }
    
    def _get_current_environment(self) -> str:
        """获取当前环境"""
        # 从环境变量获取
        env = os.getenv("WOLFKILL_ENV", os.getenv("NODE_ENV", "development"))
        
        # 验证环境名称
        valid_environments = ["development", "testing", "production"]
        if env not in valid_environments:
            env = "development"
        
        return env
    
    def apply_environment_variables(self, service: str, environment: str = None):
        """应用环境变量到当前进程"""
        if environment is None:
            environment = self._get_current_environment()
        
        env_vars = self.get_environment_variables(service, environment)
        
        for key, value in env_vars.items():
            os.environ[key] = value
    
    def validate_service_config(self, service: str, environment: str = None) -> List[str]:
        """验证服务配置"""
        if environment is None:
            environment = self._get_current_environment()
        
        errors = []
        
        try:
            if service == "backend":
                config = self.get_backend_config(environment)
                
                # 检查端口是否可用
                if not self._is_port_available(config.host, config.port):
                    errors.append(f"端口 {config.port} 已被占用")
                
            elif service == "frontend":
                config = self.get_frontend_config(environment)
                
                # 检查端口是否可用
                if not self._is_port_available(config.host, config.port):
                    errors.append(f"端口 {config.port} 已被占用")
                
                # 检查后端连接
                api_config = self.get_api_connection_config(environment)
                if not self._is_url_accessible(api_config["base_url"]):
                    errors.append(f"无法连接到后端API: {api_config['base_url']}")
            
        except Exception as e:
            errors.append(f"配置验证失败: {str(e)}")
        
        return errors
    
    def _is_port_available(self, host: str, port: int) -> bool:
        """检查端口是否可用"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0  # 端口可用时connect_ex返回非0
        except Exception:
            return False
    
    def _is_url_accessible(self, url: str) -> bool:
        """检查URL是否可访问"""
        try:
            import requests
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except Exception:
            return False


# 全局服务配置管理器实例
services_config_manager = ServicesConfigManager()


def get_backend_config(environment: str = None) -> ServiceConfig:
    """获取后端配置的便捷函数"""
    return services_config_manager.get_backend_config(environment)


def get_frontend_config(environment: str = None) -> ServiceConfig:
    """获取前端配置的便捷函数"""
    return services_config_manager.get_frontend_config(environment)


def get_api_connection_config(environment: str = None) -> Dict[str, Any]:
    """获取API连接配置的便捷函数"""
    return services_config_manager.get_api_connection_config(environment)
