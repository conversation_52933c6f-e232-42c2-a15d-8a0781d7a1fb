#!/usr/bin/env python3
"""
狼人杀AI游戏后端启动脚本
"""

import os
import sys
import subprocess

def main():
    print("🐺 狼人杀AI游戏 - 后端服务器启动")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)

    print(f"✅ Python版本: {sys.version}")

    # 检查是否在正确的目录
    if not os.path.exists('backend/api/app.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)

    # 检查依赖
    try:
        import flask
        import flask_cors
        import flask_socketio
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: cd backend && pip install -r requirements.txt")
        sys.exit(1)

    # 启动后端服务器
    print("\n🚀 启动后端API服务器...")
    print("📡 API地址: http://localhost:3001")
    print("🔌 WebSocket地址: ws://localhost:3001")
    print("📖 API文档: http://localhost:3001/api/health")
    print("\n按 Ctrl+C 停止服务器")
    print("-" * 50)

    try:
        os.chdir('backend')
        subprocess.run([sys.executable, 'api/app.py'])
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()