#!/usr/bin/env python3
"""
狼人杀AI游戏后端启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.config.services_config import get_backend_config, services_config_manager
    USE_CONFIG_SYSTEM = True
except ImportError:
    USE_CONFIG_SYSTEM = False

def main():
    print("🐺 狼人杀AI游戏 - 后端服务器启动")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)

    print(f"✅ Python版本: {sys.version}")

    # 检查是否在正确的目录
    if not os.path.exists('backend/api/app.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)

    # 检查依赖
    try:
        import flask
        import flask_cors
        import flask_socketio
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: cd backend && pip install -r requirements.txt")
        sys.exit(1)

    # 获取配置
    if USE_CONFIG_SYSTEM:
        try:
            backend_config = get_backend_config()

            # 应用环境变量
            services_config_manager.apply_environment_variables("backend")

            print(f"✅ 使用配置文件: 端口 {backend_config.port}")
            api_url = f"http://localhost:{backend_config.port}"
            websocket_url = f"ws://localhost:{backend_config.port}"
        except Exception as e:
            print(f"⚠️  配置系统错误，使用默认配置: {e}")
            api_url = "http://localhost:8000"
            websocket_url = "ws://localhost:8000"
    else:
        print("⚠️  配置系统不可用，使用默认配置")
        api_url = "http://localhost:8000"
        websocket_url = "ws://localhost:8000"

    # 启动后端服务器
    print("\n🚀 启动后端API服务器...")
    print(f"📡 API地址: {api_url}")
    print(f"🔌 WebSocket地址: {websocket_url}")
    print(f"📖 API文档: {api_url}/api/health")
    print("\n按 Ctrl+C 停止服务器")
    print("-" * 50)

    try:
        os.chdir('backend')
        subprocess.run([sys.executable, 'api/app.py'])
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()