#!/bin/bash

# 狼人杀AI游戏前端启动脚本

echo "🐺 狼人杀AI游戏 - 前端启动"
echo "=================================================="

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 16+"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    exit 1
fi

echo "✅ npm版本: $(npm --version)"

# 检查前端目录
if [ ! -d "frontend/src" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 进入前端目录
cd frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "✅ 依赖检查通过"

# 设置环境变量的优先级：
# 1. 使用增强环境变量脚本（支持变量替换）
# 2. 从配置文件获取设置
# 3. 使用默认设置

if [ -f "scripts/setup_env.py" ] && [ -f ".env" ] && command -v python3 >/dev/null 2>&1; then
    echo "📋 使用增强环境变量脚本..."

    # 使用支持变量替换的环境变量脚本
    ENV_OUTPUT=$(python3 scripts/setup_env.py --env-file .env 2>/dev/null | grep "^export")

    if [ -n "$ENV_OUTPUT" ]; then
        echo "✅ 应用环境变量（支持变量替换）"
        eval "$ENV_OUTPUT"
    else
        echo "⚠️  环境变量脚本执行失败，尝试配置文件..."
        USE_CONFIG_FILE=true
    fi
elif [ -f "configs/services_config.yaml" ] && command -v python3 >/dev/null 2>&1; then
    USE_CONFIG_FILE=true
else
    USE_DEFAULT=true
fi

# 回退到配置文件
if [ "$USE_CONFIG_FILE" = "true" ]; then
    echo "📋 读取服务配置文件..."

    CONFIG_OUTPUT=$(python3 -c "
import sys
import os
sys.path.insert(0, '.')
try:
    from src.config.services_config import get_api_connection_config
    config = get_api_connection_config()
    print(f\"API_URL={config['base_url']}\")
    print(f\"WEBSOCKET_URL={config['websocket_url']}\")
except Exception as e:
    print('ERROR')
" 2>/dev/null)

    if [ "$CONFIG_OUTPUT" != "ERROR" ] && [ -n "$CONFIG_OUTPUT" ]; then
        echo "✅ 使用配置文件设置"
        eval "$CONFIG_OUTPUT"
        export REACT_APP_API_URL="$API_URL"
        export REACT_APP_WEBSOCKET_URL="$WEBSOCKET_URL"
    else
        USE_DEFAULT=true
    fi
fi

# 回退到默认设置
if [ "$USE_DEFAULT" = "true" ]; then
    echo "⚠️  使用默认设置"

    # 如果设置了 BACKEND_PORT，使用它；否则使用默认值
    BACKEND_PORT=${BACKEND_PORT:-8000}

    export REACT_APP_API_URL=http://localhost:${BACKEND_PORT}/api
    export REACT_APP_WEBSOCKET_URL=http://localhost:${BACKEND_PORT}

    echo "  后端端口: $BACKEND_PORT"
fi

echo ""
echo "🚀 启动前端开发服务器..."
echo "🌐 前端地址: http://localhost:3000"
echo "📡 API地址: http://localhost:8000"
echo ""
echo "请确保后端服务器已启动 (运行 python start_backend.py)"
echo "按 Ctrl+C 停止服务器"
echo "--------------------------------------------------"

# 启动前端服务器
npm start