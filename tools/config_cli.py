#!/usr/bin/env python3
"""
配置管理CLI工具
提供配置文件的验证、管理和操作功能
"""
import sys
import os
import argparse
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from tabulate import tabulate
import colorama
from colorama import Fore, Style

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.config_manager import ConfigManager, ConfigSchema


class ConfigCLI:
    """配置管理CLI"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        colorama.init()
    
    def validate_config(self, config_name: str, file_path: Optional[str] = None) -> bool:
        """验证配置文件"""
        try:
            if file_path:
                self.config_manager.load_config(config_name, file_path)
            else:
                self.config_manager.load_config(config_name)
            
            print(f"{Fore.GREEN}✓ 配置 {config_name} 验证通过{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{Fore.RED}✗ 配置 {config_name} 验证失败: {e}{Style.RESET_ALL}")
            return False
    
    def validate_all_configs(self) -> Dict[str, bool]:
        """验证所有配置文件"""
        print(f"{Fore.CYAN}正在验证所有配置文件...{Style.RESET_ALL}\n")
        
        config_files = [
            ("ai_config", "configs/ai_config.yaml"),
            ("game_config", "configs/game_config.yaml"),
            ("llm_config", "configs/llm_config.yaml")
        ]
        
        results = {}
        for config_name, file_path in config_files:
            if Path(file_path).exists():
                results[config_name] = self.validate_config(config_name, file_path)
            else:
                print(f"{Fore.YELLOW}⚠ 配置文件不存在: {file_path}{Style.RESET_ALL}")
                results[config_name] = False
        
        # 显示总结
        passed = sum(results.values())
        total = len(results)
        
        print(f"\n{Fore.CYAN}验证结果: {passed}/{total} 通过{Style.RESET_ALL}")
        
        if passed == total:
            print(f"{Fore.GREEN}所有配置文件验证通过！{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}有 {total - passed} 个配置文件验证失败{Style.RESET_ALL}")
        
        return results
    
    def list_configs(self):
        """列出所有配置"""
        configs = []
        
        # 检查配置文件
        config_files = [
            ("ai_config", "configs/ai_config.yaml"),
            ("game_config", "configs/game_config.yaml"),
            ("llm_config", "configs/llm_config.yaml")
        ]
        
        for config_name, file_path in config_files:
            path = Path(file_path)
            status = "✓" if path.exists() else "✗"
            size = f"{path.stat().st_size} bytes" if path.exists() else "N/A"
            
            configs.append([
                config_name,
                file_path,
                status,
                size
            ])
        
        headers = ["配置名称", "文件路径", "状态", "大小"]
        print(tabulate(configs, headers=headers, tablefmt="grid"))
    
    def show_config(self, config_name: str, key: Optional[str] = None):
        """显示配置内容"""
        try:
            config_data = self.config_manager.get_config(config_name, key)
            
            if config_data is None:
                print(f"{Fore.RED}配置不存在: {config_name}.{key or ''}{Style.RESET_ALL}")
                return
            
            print(f"{Fore.CYAN}配置内容 ({config_name}{('.' + key) if key else ''})::{Style.RESET_ALL}")
            print(yaml.dump(config_data, default_flow_style=False, allow_unicode=True, indent=2))
            
        except Exception as e:
            print(f"{Fore.RED}获取配置失败: {e}{Style.RESET_ALL}")
    
    def set_config(self, config_name: str, key: str, value: str, save: bool = True):
        """设置配置值"""
        try:
            # 尝试解析值
            parsed_value = self._parse_value(value)
            
            self.config_manager.set_config(config_name, key, parsed_value, save)
            print(f"{Fore.GREEN}✓ 配置已更新: {config_name}.{key} = {parsed_value}{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}设置配置失败: {e}{Style.RESET_ALL}")
    
    def _parse_value(self, value: str) -> Any:
        """解析配置值"""
        # 尝试解析为JSON
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            pass
        
        # 尝试解析为布尔值
        if value.lower() in ['true', 'false']:
            return value.lower() == 'true'
        
        # 尝试解析为数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value
    
    def export_config(self, config_name: str, output_file: str, format: str = "yaml"):
        """导出配置"""
        try:
            config_str = self.config_manager.export_config(config_name, format)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(config_str)
            
            print(f"{Fore.GREEN}✓ 配置已导出到: {output_file}{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}导出配置失败: {e}{Style.RESET_ALL}")
    
    def import_config(self, config_name: str, input_file: str, format: str = "yaml"):
        """导入配置"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                config_str = f.read()
            
            self.config_manager.import_config(config_name, config_str, format)
            print(f"{Fore.GREEN}✓ 配置已从 {input_file} 导入{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}导入配置失败: {e}{Style.RESET_ALL}")
    
    def create_default_config(self, config_name: str, output_file: Optional[str] = None):
        """创建默认配置"""
        try:
            default_config = self.config_manager.create_default_config(config_name)
            
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True, indent=2)
                print(f"{Fore.GREEN}✓ 默认配置已创建: {output_file}{Style.RESET_ALL}")
            else:
                print(f"{Fore.CYAN}默认配置 ({config_name}):{Style.RESET_ALL}")
                print(yaml.dump(default_config, default_flow_style=False, allow_unicode=True, indent=2))
            
        except Exception as e:
            print(f"{Fore.RED}创建默认配置失败: {e}{Style.RESET_ALL}")
    
    def config_info(self, config_name: str):
        """显示配置信息"""
        try:
            info = self.config_manager.get_config_info(config_name)
            
            print(f"{Fore.CYAN}配置信息 ({config_name}):{Style.RESET_ALL}")
            print(f"  名称: {info['name']}")
            print(f"  已加载: {'是' if info['loaded'] else '否'}")
            print(f"  文件路径: {info['file_path']}")
            print(f"  模式可用: {'是' if info['schema_available'] else '否'}")
            print(f"  最后修改: {info.get('last_modified', '未知')}")
            
            if 'schema' in info:
                schema = info['schema']
                print(f"  描述: {schema['description']}")
                print(f"  必需字段: {', '.join(schema['required_fields'])}")
                print(f"  可选字段: {', '.join(schema['optional_fields'])}")
            
        except Exception as e:
            print(f"{Fore.RED}获取配置信息失败: {e}{Style.RESET_ALL}")
    
    def watch_configs(self):
        """监控配置文件变化"""
        print(f"{Fore.CYAN}启动配置文件监控...{Style.RESET_ALL}")
        print("按 Ctrl+C 停止监控")
        
        try:
            self.config_manager.enable_hot_reload()
            
            # 保持程序运行
            import time
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}停止监控{Style.RESET_ALL}")
            self.config_manager.disable_hot_reload()
    
    def benchmark_configs(self):
        """配置性能基准测试"""
        import time
        
        print(f"{Fore.CYAN}开始配置性能基准测试...{Style.RESET_ALL}")
        
        configs = ["ai_config", "game_config", "llm_config"]
        results = []
        
        for config_name in configs:
            try:
                # 测试加载时间
                start_time = time.time()
                self.config_manager.load_config(config_name)
                load_time = time.time() - start_time
                
                # 测试获取时间
                start_time = time.time()
                for _ in range(100):
                    self.config_manager.get_config(config_name, "default_settings")
                get_time = (time.time() - start_time) / 100
                
                results.append([
                    config_name,
                    f"{load_time:.4f}s",
                    f"{get_time:.6f}s",
                    "✓"
                ])
                
            except Exception as e:
                results.append([
                    config_name,
                    "N/A",
                    "N/A",
                    f"✗ {str(e)[:30]}..."
                ])
        
        headers = ["配置名称", "加载时间", "获取时间", "状态"]
        print(tabulate(results, headers=headers, tablefmt="grid"))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置管理CLI工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 验证命令
    validate_parser = subparsers.add_parser("validate", help="验证配置文件")
    validate_parser.add_argument("config", nargs="?", help="配置名称")
    validate_parser.add_argument("--file", help="配置文件路径")
    validate_parser.add_argument("--all", action="store_true", help="验证所有配置")
    
    # 列表命令
    subparsers.add_parser("list", help="列出所有配置")
    
    # 显示命令
    show_parser = subparsers.add_parser("show", help="显示配置内容")
    show_parser.add_argument("config", help="配置名称")
    show_parser.add_argument("--key", help="配置键")
    
    # 设置命令
    set_parser = subparsers.add_parser("set", help="设置配置值")
    set_parser.add_argument("config", help="配置名称")
    set_parser.add_argument("key", help="配置键")
    set_parser.add_argument("value", help="配置值")
    set_parser.add_argument("--no-save", action="store_true", help="不保存到文件")
    
    # 导出命令
    export_parser = subparsers.add_parser("export", help="导出配置")
    export_parser.add_argument("config", help="配置名称")
    export_parser.add_argument("output", help="输出文件")
    export_parser.add_argument("--format", choices=["yaml", "json"], default="yaml", help="输出格式")
    
    # 导入命令
    import_parser = subparsers.add_parser("import", help="导入配置")
    import_parser.add_argument("config", help="配置名称")
    import_parser.add_argument("input", help="输入文件")
    import_parser.add_argument("--format", choices=["yaml", "json"], default="yaml", help="输入格式")
    
    # 默认配置命令
    default_parser = subparsers.add_parser("default", help="创建默认配置")
    default_parser.add_argument("config", help="配置名称")
    default_parser.add_argument("--output", help="输出文件")
    
    # 信息命令
    info_parser = subparsers.add_parser("info", help="显示配置信息")
    info_parser.add_argument("config", help="配置名称")
    
    # 监控命令
    subparsers.add_parser("watch", help="监控配置文件变化")
    
    # 基准测试命令
    subparsers.add_parser("benchmark", help="配置性能基准测试")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = ConfigCLI()
    
    try:
        if args.command == "validate":
            if args.all:
                cli.validate_all_configs()
            elif args.config:
                cli.validate_config(args.config, args.file)
            else:
                cli.validate_all_configs()
        
        elif args.command == "list":
            cli.list_configs()
        
        elif args.command == "show":
            cli.show_config(args.config, args.key)
        
        elif args.command == "set":
            cli.set_config(args.config, args.key, args.value, not args.no_save)
        
        elif args.command == "export":
            cli.export_config(args.config, args.output, args.format)
        
        elif args.command == "import":
            cli.import_config(args.config, args.input, args.format)
        
        elif args.command == "default":
            cli.create_default_config(args.config, args.output)
        
        elif args.command == "info":
            cli.config_info(args.config)
        
        elif args.command == "watch":
            cli.watch_configs()
        
        elif args.command == "benchmark":
            cli.benchmark_configs()
    
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}操作已取消{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}错误: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
