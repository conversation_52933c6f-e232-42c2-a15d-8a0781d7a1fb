#!/usr/bin/env python3
"""
配置迁移工具
帮助用户从旧的硬编码配置迁移到新的配置文件系统
"""
import sys
import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.config_manager import ConfigManager


class ConfigMigration:
    """配置迁移工具"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.migration_log = []
    
    def migrate_all_configs(self, backup: bool = True) -> bool:
        """迁移所有配置"""
        print("🔄 开始配置迁移...")
        
        success = True
        
        # 备份现有配置
        if backup:
            self._backup_existing_configs()
        
        # 迁移各类配置
        if not self._migrate_ai_config():
            success = False
        
        if not self._migrate_game_config():
            success = False
        
        if not self._migrate_llm_config():
            success = False
        
        # 生成迁移报告
        self._generate_migration_report()
        
        if success:
            print("✅ 配置迁移完成！")
        else:
            print("❌ 配置迁移过程中出现错误，请查看迁移报告")
        
        return success
    
    def _backup_existing_configs(self):
        """备份现有配置"""
        print("📦 备份现有配置...")
        
        backup_dir = Path("config_backup")
        backup_dir.mkdir(exist_ok=True)
        
        # 备份现有配置文件
        config_files = [
            "configs/ai_config.yaml",
            "configs/game_config.yaml", 
            "configs/llm_config.yaml"
        ]
        
        for config_file in config_files:
            config_path = Path(config_file)
            if config_path.exists():
                backup_path = backup_dir / config_path.name
                import shutil
                shutil.copy2(config_path, backup_path)
                self.migration_log.append(f"备份配置文件: {config_file} -> {backup_path}")
    
    def _migrate_ai_config(self) -> bool:
        """迁移AI配置"""
        print("🤖 迁移AI配置...")
        
        try:
            # 检查是否已存在配置文件
            ai_config_path = Path("configs/ai_config.yaml")
            if ai_config_path.exists():
                print("  AI配置文件已存在，跳过迁移")
                return True
            
            # 从代码中提取配置
            ai_config = self._extract_ai_config_from_code()
            
            # 保存配置
            self.config_manager.configs["ai_config"] = ai_config
            self.config_manager.save_config("ai_config", ai_config_path)
            
            self.migration_log.append("成功迁移AI配置")
            print("  ✅ AI配置迁移完成")
            return True
            
        except Exception as e:
            self.migration_log.append(f"AI配置迁移失败: {e}")
            print(f"  ❌ AI配置迁移失败: {e}")
            return False
    
    def _migrate_game_config(self) -> bool:
        """迁移游戏配置"""
        print("🎮 迁移游戏配置...")
        
        try:
            # 检查是否已存在配置文件
            game_config_path = Path("configs/game_config.yaml")
            if game_config_path.exists():
                print("  游戏配置文件已存在，跳过迁移")
                return True
            
            # 从代码中提取配置
            game_config = self._extract_game_config_from_code()
            
            # 保存配置
            self.config_manager.configs["game_config"] = game_config
            self.config_manager.save_config("game_config", game_config_path)
            
            self.migration_log.append("成功迁移游戏配置")
            print("  ✅ 游戏配置迁移完成")
            return True
            
        except Exception as e:
            self.migration_log.append(f"游戏配置迁移失败: {e}")
            print(f"  ❌ 游戏配置迁移失败: {e}")
            return False
    
    def _migrate_llm_config(self) -> bool:
        """迁移LLM配置"""
        print("🧠 迁移LLM配置...")
        
        try:
            # 检查是否已存在配置文件
            llm_config_path = Path("configs/llm_config.yaml")
            if llm_config_path.exists():
                print("  LLM配置文件已存在，跳过迁移")
                return True
            
            # 从代码中提取配置
            llm_config = self._extract_llm_config_from_code()
            
            # 保存配置
            self.config_manager.configs["llm_config"] = llm_config
            self.config_manager.save_config("llm_config", llm_config_path)
            
            self.migration_log.append("成功迁移LLM配置")
            print("  ✅ LLM配置迁移完成")
            return True
            
        except Exception as e:
            self.migration_log.append(f"LLM配置迁移失败: {e}")
            print(f"  ❌ LLM配置迁移失败: {e}")
            return False
    
    def _extract_ai_config_from_code(self) -> Dict[str, Any]:
        """从代码中提取AI配置"""
        # 这里返回默认的AI配置结构
        # 在实际实现中，可以从现有代码中解析配置
        return self.config_manager.create_default_config("ai_config")
    
    def _extract_game_config_from_code(self) -> Dict[str, Any]:
        """从代码中提取游戏配置"""
        # 这里返回默认的游戏配置结构
        return self.config_manager.create_default_config("game_config")
    
    def _extract_llm_config_from_code(self) -> Dict[str, Any]:
        """从代码中提取LLM配置"""
        # 这里返回默认的LLM配置结构
        return self.config_manager.create_default_config("llm_config")
    
    def _generate_migration_report(self):
        """生成迁移报告"""
        report_path = Path("migration_report.txt")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("配置迁移报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"迁移时间: {self._get_current_time()}\n")
            f.write(f"迁移项目: {len(self.migration_log)} 项\n\n")
            
            f.write("迁移详情:\n")
            for i, log_entry in enumerate(self.migration_log, 1):
                f.write(f"{i}. {log_entry}\n")
            
            f.write("\n配置文件位置:\n")
            f.write("- AI配置: configs/ai_config.yaml\n")
            f.write("- 游戏配置: configs/game_config.yaml\n")
            f.write("- LLM配置: configs/llm_config.yaml\n")
            
            f.write("\n使用说明:\n")
            f.write("1. 使用 python tools/config_cli.py validate --all 验证配置\n")
            f.write("2. 使用 python tools/config_cli.py show <config_name> 查看配置\n")
            f.write("3. 使用 python examples/config_usage_example.py 查看使用示例\n")
        
        print(f"📋 迁移报告已生成: {report_path}")
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def validate_migration(self) -> bool:
        """验证迁移结果"""
        print("🔍 验证迁移结果...")
        
        validation_results = self.config_manager.validate_all_configs()
        
        all_valid = True
        for config_name, result in validation_results.items():
            if result["valid"]:
                print(f"  ✅ {config_name}: 验证通过")
            else:
                print(f"  ❌ {config_name}: 验证失败")
                for error in result["errors"]:
                    print(f"    错误: {error}")
                all_valid = False
        
        return all_valid
    
    def create_environment_template(self):
        """创建环境变量模板"""
        print("📝 创建环境变量模板...")
        
        env_template = """# 狼人杀AI游戏环境变量配置模板
# 复制此文件为 .env 并根据需要修改配置

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic API配置  
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# AI配置覆盖示例
# WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ENABLED=true
# WOLFKILL_AI_CONFIG_LEARNING_SETTINGS_ADAPTATION_RATE=0.2

# 游戏配置覆盖示例
# WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_GAME_MODE=balanced
# WOLFKILL_GAME_CONFIG_DEFAULT_SETTINGS_AUTO_START=true

# LLM配置覆盖示例
# WOLFKILL_LLM_CONFIG_DEFAULT_PROVIDER=openai_gpt4
# WOLFKILL_LLM_CONFIG_PROVIDERS_QWEN3_30B_BASE_URL=http://localhost:8005/v1
"""
        
        env_path = Path(".env.template")
        with open(env_path, 'w', encoding='utf-8') as f:
            f.write(env_template)
        
        print(f"  ✅ 环境变量模板已创建: {env_path}")
        self.migration_log.append(f"创建环境变量模板: {env_path}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置迁移工具")
    parser.add_argument("--no-backup", action="store_true", help="不备份现有配置")
    parser.add_argument("--validate-only", action="store_true", help="仅验证现有配置")
    parser.add_argument("--create-env-template", action="store_true", help="创建环境变量模板")
    
    args = parser.parse_args()
    
    migration = ConfigMigration()
    
    try:
        if args.validate_only:
            success = migration.validate_migration()
            sys.exit(0 if success else 1)
        
        if args.create_env_template:
            migration.create_environment_template()
            return
        
        # 执行迁移
        success = migration.migrate_all_configs(backup=not args.no_backup)
        
        # 验证迁移结果
        if success:
            validation_success = migration.validate_migration()
            if not validation_success:
                print("⚠️  迁移完成但验证失败，请检查配置文件")
                success = False
        
        # 创建环境变量模板
        migration.create_environment_template()
        
        print("\n🎉 迁移完成！")
        print("📖 请查看 migration_report.txt 了解详细信息")
        print("🔧 使用 python tools/config_cli.py --help 查看配置管理命令")
        print("📚 运行 python examples/config_usage_example.py 查看使用示例")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  迁移已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
