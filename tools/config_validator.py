#!/usr/bin/env python3
"""
配置验证工具
提供详细的配置文件验证和错误报告功能
"""
import sys
import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import re

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.config_manager import ConfigManager


@dataclass
class ValidationError:
    """验证错误"""
    level: str  # ERROR, WARNING, INFO
    category: str  # SYNTAX, SCHEMA, VALUE, REFERENCE
    message: str
    path: str
    line: Optional[int] = None
    suggestion: Optional[str] = None


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.errors: List[ValidationError] = []
    
    def validate_file(self, file_path: str) -> <PERSON><PERSON>[bool, List[ValidationError]]:
        """验证配置文件"""
        self.errors = []
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.errors.append(ValidationError(
                level="ERROR",
                category="FILE",
                message=f"配置文件不存在: {file_path}",
                path=str(file_path)
            ))
            return False, self.errors
        
        # 语法验证
        config_data = self._validate_syntax(file_path)
        if config_data is None:
            return False, self.errors
        
        # 结构验证
        config_name = self._infer_config_name(file_path)
        if config_name:
            self._validate_structure(config_name, config_data, str(file_path))
        
        # 值验证
        self._validate_values(config_data, str(file_path))
        
        # 引用验证
        self._validate_references(config_data, str(file_path))
        
        # 逻辑验证
        self._validate_logic(config_data, str(file_path))
        
        # 检查是否有错误
        has_errors = any(error.level == "ERROR" for error in self.errors)
        return not has_errors, self.errors
    
    def _validate_syntax(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """验证语法"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                config_data = yaml.safe_load(content)
            elif file_path.suffix.lower() == '.json':
                config_data = json.loads(content)
            else:
                self.errors.append(ValidationError(
                    level="ERROR",
                    category="SYNTAX",
                    message=f"不支持的文件格式: {file_path.suffix}",
                    path=str(file_path),
                    suggestion="支持的格式: .yaml, .yml, .json"
                ))
                return None
            
            if config_data is None:
                self.errors.append(ValidationError(
                    level="WARNING",
                    category="SYNTAX",
                    message="配置文件为空",
                    path=str(file_path)
                ))
                return {}
            
            return config_data
            
        except yaml.YAMLError as e:
            self.errors.append(ValidationError(
                level="ERROR",
                category="SYNTAX",
                message=f"YAML语法错误: {e}",
                path=str(file_path),
                line=getattr(e, 'problem_mark', {}).get('line', None),
                suggestion="检查YAML语法，注意缩进和特殊字符"
            ))
            return None
        
        except json.JSONDecodeError as e:
            self.errors.append(ValidationError(
                level="ERROR",
                category="SYNTAX",
                message=f"JSON语法错误: {e}",
                path=str(file_path),
                line=e.lineno,
                suggestion="检查JSON语法，注意括号匹配和逗号"
            ))
            return None
        
        except Exception as e:
            self.errors.append(ValidationError(
                level="ERROR",
                category="SYNTAX",
                message=f"读取文件失败: {e}",
                path=str(file_path)
            ))
            return None
    
    def _infer_config_name(self, file_path: Path) -> Optional[str]:
        """推断配置名称"""
        name = file_path.stem
        
        # 已知的配置类型
        known_configs = {
            "ai_config": "ai_config",
            "game_config": "game_config", 
            "llm_config": "llm_config"
        }
        
        return known_configs.get(name)
    
    def _validate_structure(self, config_name: str, config_data: Dict[str, Any], file_path: str):
        """验证结构"""
        if config_name not in self.config_manager.schemas:
            self.errors.append(ValidationError(
                level="WARNING",
                category="SCHEMA",
                message=f"未知的配置类型: {config_name}",
                path=file_path
            ))
            return
        
        schema = self.config_manager.schemas[config_name]
        
        # 检查必需字段
        for field in schema.required_fields:
            if field not in config_data:
                self.errors.append(ValidationError(
                    level="ERROR",
                    category="SCHEMA",
                    message=f"缺少必需字段: {field}",
                    path=f"{file_path}.{field}",
                    suggestion=f"添加字段 {field}"
                ))
        
        # 检查字段类型
        for field, expected_type in schema.field_types.items():
            if field in config_data:
                value = config_data[field]
                if not isinstance(value, expected_type):
                    self.errors.append(ValidationError(
                        level="ERROR",
                        category="SCHEMA",
                        message=f"字段 {field} 类型错误，期望 {expected_type.__name__}，实际 {type(value).__name__}",
                        path=f"{file_path}.{field}",
                        suggestion=f"将 {field} 的值改为 {expected_type.__name__} 类型"
                    ))
        
        # 检查未知字段
        known_fields = set(schema.required_fields + schema.optional_fields)
        for field in config_data:
            if field not in known_fields:
                self.errors.append(ValidationError(
                    level="WARNING",
                    category="SCHEMA",
                    message=f"未知字段: {field}",
                    path=f"{file_path}.{field}",
                    suggestion="检查字段名是否正确"
                ))
    
    def _validate_values(self, config_data: Dict[str, Any], file_path: str, path_prefix: str = ""):
        """验证值"""
        for key, value in config_data.items():
            current_path = f"{path_prefix}.{key}" if path_prefix else key
            full_path = f"{file_path}.{current_path}"
            
            if isinstance(value, dict):
                self._validate_values(value, file_path, current_path)
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        self._validate_values(item, file_path, f"{current_path}[{i}]")
            else:
                self._validate_single_value(key, value, full_path)
    
    def _validate_single_value(self, key: str, value: Any, path: str):
        """验证单个值"""
        # 数值范围检查
        if isinstance(value, (int, float)):
            if key.endswith(('_rate', '_ratio', '_probability', '_threshold', '_accuracy')):
                if not 0 <= value <= 1:
                    self.errors.append(ValidationError(
                        level="WARNING",
                        category="VALUE",
                        message=f"比率值 {key} 应该在 0-1 之间，当前值: {value}",
                        path=path,
                        suggestion="将值设置为 0-1 之间的小数"
                    ))
            
            if key.endswith('_time') and value < 0:
                self.errors.append(ValidationError(
                    level="ERROR",
                    category="VALUE",
                    message=f"时间值 {key} 不能为负数，当前值: {value}",
                    path=path,
                    suggestion="设置为正数"
                ))
        
        # 字符串检查
        if isinstance(value, str):
            if key.endswith('_url') and value:
                if not re.match(r'^https?://', value):
                    self.errors.append(ValidationError(
                        level="WARNING",
                        category="VALUE",
                        message=f"URL格式可能不正确: {value}",
                        path=path,
                        suggestion="确保URL以 http:// 或 https:// 开头"
                    ))
            
            if key == 'api_key' and value in ['', 'your-api-key', 'CHANGE_ME']:
                self.errors.append(ValidationError(
                    level="WARNING",
                    category="VALUE",
                    message="API密钥未设置或使用默认值",
                    path=path,
                    suggestion="设置正确的API密钥"
                ))
    
    def _validate_references(self, config_data: Dict[str, Any], file_path: str):
        """验证引用"""
        # 检查环境变量引用
        self._check_env_references(config_data, file_path)
        
        # 检查内部引用
        self._check_internal_references(config_data, file_path)
    
    def _check_env_references(self, config_data: Dict[str, Any], file_path: str, path_prefix: str = ""):
        """检查环境变量引用"""
        for key, value in config_data.items():
            current_path = f"{path_prefix}.{key}" if path_prefix else key
            full_path = f"{file_path}.{current_path}"
            
            if isinstance(value, dict):
                self._check_env_references(value, file_path, current_path)
            elif isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                if env_var not in os.environ:
                    self.errors.append(ValidationError(
                        level="WARNING",
                        category="REFERENCE",
                        message=f"环境变量未设置: {env_var}",
                        path=full_path,
                        suggestion=f"设置环境变量 {env_var}"
                    ))
    
    def _check_internal_references(self, config_data: Dict[str, Any], file_path: str):
        """检查内部引用"""
        # 检查角色引用
        if 'role_distribution' in config_data:
            role_dist = config_data['role_distribution']
            valid_roles = ['VILLAGER', 'WEREWOLF', 'SEER', 'WITCH', 'GUARD', 'HUNTER']
            
            for role in role_dist:
                if role not in valid_roles:
                    self.errors.append(ValidationError(
                        level="ERROR",
                        category="REFERENCE",
                        message=f"无效的角色类型: {role}",
                        path=f"{file_path}.role_distribution.{role}",
                        suggestion=f"使用有效的角色类型: {', '.join(valid_roles)}"
                    ))
        
        # 检查难度等级引用
        if 'difficulty' in config_data:
            difficulty = config_data['difficulty']
            valid_difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master']
            
            if difficulty not in valid_difficulties:
                self.errors.append(ValidationError(
                    level="ERROR",
                    category="REFERENCE",
                    message=f"无效的难度等级: {difficulty}",
                    path=f"{file_path}.difficulty",
                    suggestion=f"使用有效的难度等级: {', '.join(valid_difficulties)}"
                ))
    
    def _validate_logic(self, config_data: Dict[str, Any], file_path: str):
        """验证逻辑"""
        # 检查玩家数量逻辑
        if 'min_players' in config_data and 'max_players' in config_data:
            min_players = config_data['min_players']
            max_players = config_data['max_players']
            
            if min_players > max_players:
                self.errors.append(ValidationError(
                    level="ERROR",
                    category="LOGIC",
                    message=f"最小玩家数 ({min_players}) 大于最大玩家数 ({max_players})",
                    path=f"{file_path}.min_players",
                    suggestion="确保最小玩家数不大于最大玩家数"
                ))
        
        # 检查角色分配逻辑
        if 'role_distribution' in config_data and 'recommended_players' in config_data:
            role_dist = config_data['role_distribution']
            recommended = config_data['recommended_players']
            
            total_roles = sum(role_dist.values()) if isinstance(role_dist, dict) else 0
            
            if total_roles != recommended:
                self.errors.append(ValidationError(
                    level="WARNING",
                    category="LOGIC",
                    message=f"角色总数 ({total_roles}) 与推荐玩家数 ({recommended}) 不匹配",
                    path=f"{file_path}.role_distribution",
                    suggestion="调整角色分配或推荐玩家数"
                ))
        
        # 检查时间设置逻辑
        if 'discussion_time_limit' in config_data and 'voting_time_limit' in config_data:
            discussion_time = config_data['discussion_time_limit']
            voting_time = config_data['voting_time_limit']
            
            if voting_time > discussion_time:
                self.errors.append(ValidationError(
                    level="WARNING",
                    category="LOGIC",
                    message=f"投票时间 ({voting_time}) 大于讨论时间 ({discussion_time})",
                    path=f"{file_path}.voting_time_limit",
                    suggestion="通常投票时间应该小于讨论时间"
                ))
    
    def generate_report(self, errors: List[ValidationError]) -> str:
        """生成验证报告"""
        if not errors:
            return "✅ 配置验证通过，未发现问题。"
        
        report = []
        report.append("📋 配置验证报告")
        report.append("=" * 50)
        
        # 按级别分组
        error_count = sum(1 for e in errors if e.level == "ERROR")
        warning_count = sum(1 for e in errors if e.level == "WARNING")
        info_count = sum(1 for e in errors if e.level == "INFO")
        
        report.append(f"总计: {len(errors)} 个问题")
        report.append(f"  ❌ 错误: {error_count}")
        report.append(f"  ⚠️  警告: {warning_count}")
        report.append(f"  ℹ️  信息: {info_count}")
        report.append("")
        
        # 详细问题列表
        for i, error in enumerate(errors, 1):
            icon = {"ERROR": "❌", "WARNING": "⚠️", "INFO": "ℹ️"}[error.level]
            report.append(f"{i}. {icon} [{error.category}] {error.message}")
            report.append(f"   路径: {error.path}")
            if error.line:
                report.append(f"   行号: {error.line}")
            if error.suggestion:
                report.append(f"   建议: {error.suggestion}")
            report.append("")
        
        return "\n".join(report)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置验证工具")
    parser.add_argument("files", nargs="+", help="要验证的配置文件")
    parser.add_argument("--report", help="保存报告到文件")
    parser.add_argument("--strict", action="store_true", help="严格模式（警告也视为错误）")
    
    args = parser.parse_args()
    
    validator = ConfigValidator()
    all_errors = []
    success_count = 0
    
    for file_path in args.files:
        print(f"验证文件: {file_path}")
        success, errors = validator.validate_file(file_path)
        
        if success and not (args.strict and any(e.level == "WARNING" for e in errors)):
            print(f"✅ {file_path} 验证通过")
            success_count += 1
        else:
            print(f"❌ {file_path} 验证失败")
        
        all_errors.extend(errors)
        print()
    
    # 生成报告
    report = validator.generate_report(all_errors)
    print(report)
    
    if args.report:
        with open(args.report, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n报告已保存到: {args.report}")
    
    # 返回状态码
    if args.strict:
        sys.exit(0 if len(all_errors) == 0 else 1)
    else:
        error_count = sum(1 for e in all_errors if e.level == "ERROR")
        sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
