#!/usr/bin/env python3
"""
服务管理CLI工具
提供服务配置验证、启动、停止等功能
"""
import sys
import os
import argparse
import json
import time
import requests
from pathlib import Path
from typing import Dict, Any, List
import subprocess
import signal
from tabulate import tabulate
import colorama
from colorama import Fore, Style

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.config.services_config import (
    services_config_manager,
    get_backend_config,
    get_frontend_config,
    get_api_connection_config
)


class ServicesCLI:
    """服务管理CLI"""
    
    def __init__(self):
        colorama.init()
        self.processes = {}
    
    def validate_services(self, environment: str = "development"):
        """验证服务配置"""
        print(f"{Fore.CYAN}验证服务配置 (环境: {environment})...{Style.RESET_ALL}\n")
        
        services = ["backend", "frontend"]
        all_valid = True
        
        for service in services:
            print(f"{Fore.YELLOW}验证 {service} 服务:{Style.RESET_ALL}")
            
            errors = services_config_manager.validate_service_config(service, environment)
            
            if not errors:
                print(f"  {Fore.GREEN}✓ {service} 配置验证通过{Style.RESET_ALL}")
            else:
                print(f"  {Fore.RED}✗ {service} 配置验证失败:{Style.RESET_ALL}")
                for error in errors:
                    print(f"    - {error}")
                all_valid = False
            
            print()
        
        if all_valid:
            print(f"{Fore.GREEN}所有服务配置验证通过！{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}有服务配置验证失败{Style.RESET_ALL}")
        
        return all_valid
    
    def show_config(self, service: str = None, environment: str = "development"):
        """显示服务配置"""
        print(f"{Fore.CYAN}服务配置信息 (环境: {environment}){Style.RESET_ALL}\n")
        
        if service is None or service == "backend":
            backend_config = get_backend_config(environment)
            print(f"{Fore.YELLOW}后端服务配置:{Style.RESET_ALL}")
            print(f"  名称: {backend_config.name}")
            print(f"  地址: {backend_config.host}:{backend_config.port}")
            print(f"  调试模式: {backend_config.debug}")
            print(f"  环境: {backend_config.environment}")
            print(f"  URL: {backend_config.get_url()}")
            print()
        
        if service is None or service == "frontend":
            frontend_config = get_frontend_config(environment)
            print(f"{Fore.YELLOW}前端服务配置:{Style.RESET_ALL}")
            print(f"  名称: {frontend_config.name}")
            print(f"  地址: {frontend_config.host}:{frontend_config.port}")
            print(f"  调试模式: {frontend_config.debug}")
            print(f"  环境: {frontend_config.environment}")
            print(f"  URL: {frontend_config.get_url()}")
            print()
        
        if service is None or service == "api":
            api_config = get_api_connection_config(environment)
            print(f"{Fore.YELLOW}API连接配置:{Style.RESET_ALL}")
            print(f"  API地址: {api_config['base_url']}")
            print(f"  WebSocket地址: {api_config['websocket_url']}")
            print(f"  超时时间: {api_config['timeout']}ms")
            print(f"  重试次数: {api_config['retry_attempts']}")
            print()
    
    def check_services_status(self, environment: str = "development"):
        """检查服务状态"""
        print(f"{Fore.CYAN}检查服务状态 (环境: {environment})...{Style.RESET_ALL}\n")
        
        backend_config = get_backend_config(environment)
        frontend_config = get_frontend_config(environment)
        
        services_status = []
        
        # 检查后端服务
        backend_status = self._check_service_health(
            "后端服务",
            f"{backend_config.get_url()}/api/health"
        )
        services_status.append(["后端服务", backend_config.get_url(), backend_status])
        
        # 检查前端服务
        frontend_status = self._check_service_health(
            "前端服务",
            frontend_config.get_url()
        )
        services_status.append(["前端服务", frontend_config.get_url(), frontend_status])
        
        # 显示状态表格
        headers = ["服务", "地址", "状态"]
        print(tabulate(services_status, headers=headers, tablefmt="grid"))
        
        return all(status == "✅ 运行中" for _, _, status in services_status)
    
    def _check_service_health(self, service_name: str, url: str, timeout: int = 5) -> str:
        """检查单个服务健康状态"""
        try:
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                return "✅ 运行中"
            else:
                return f"❌ 错误 ({response.status_code})"
        except requests.exceptions.ConnectionError:
            return "❌ 无法连接"
        except requests.exceptions.Timeout:
            return "❌ 超时"
        except Exception as e:
            return f"❌ 错误: {str(e)[:20]}..."
    
    def start_service(self, service: str, environment: str = "development"):
        """启动单个服务"""
        print(f"{Fore.CYAN}启动 {service} 服务 (环境: {environment})...{Style.RESET_ALL}")
        
        try:
            if service == "backend":
                return self._start_backend(environment)
            elif service == "frontend":
                return self._start_frontend(environment)
            else:
                print(f"{Fore.RED}未知服务: {service}{Style.RESET_ALL}")
                return False
        except Exception as e:
            print(f"{Fore.RED}启动 {service} 服务失败: {e}{Style.RESET_ALL}")
            return False
    
    def _start_backend(self, environment: str) -> bool:
        """启动后端服务"""
        backend_config = get_backend_config(environment)
        
        # 应用环境变量
        services_config_manager.apply_environment_variables("backend", environment)
        
        # 启动后端进程
        backend_dir = Path(__file__).parent.parent / "backend"
        backend_script = backend_dir / "api" / "app.py"
        
        if not backend_script.exists():
            print(f"{Fore.RED}后端脚本不存在: {backend_script}{Style.RESET_ALL}")
            return False
        
        env = os.environ.copy()
        env.update(services_config_manager.get_environment_variables("backend", environment))
        
        process = subprocess.Popen(
            [sys.executable, str(backend_script)],
            cwd=str(backend_dir),
            env=env
        )
        
        self.processes["backend"] = process
        
        # 等待服务启动
        if self._wait_for_service("backend", backend_config.get_url() + "/api/health"):
            print(f"{Fore.GREEN}✅ 后端服务已启动: {backend_config.get_url()}{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}❌ 后端服务启动失败{Style.RESET_ALL}")
            return False
    
    def _start_frontend(self, environment: str) -> bool:
        """启动前端服务"""
        frontend_config = get_frontend_config(environment)
        api_config = get_api_connection_config(environment)
        
        # 前端目录
        frontend_dir = Path(__file__).parent.parent / "frontend"
        
        if not frontend_dir.exists():
            print(f"{Fore.RED}前端目录不存在: {frontend_dir}{Style.RESET_ALL}")
            return False
        
        # 设置环境变量
        env = os.environ.copy()
        env.update(services_config_manager.get_environment_variables("frontend", environment))
        env["REACT_APP_API_URL"] = api_config["base_url"]
        env["REACT_APP_WEBSOCKET_URL"] = api_config["websocket_url"]
        env["PORT"] = str(frontend_config.port)
        
        # 启动前端进程
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=str(frontend_dir),
            env=env
        )
        
        self.processes["frontend"] = process
        
        # 等待服务启动
        if self._wait_for_service("frontend", frontend_config.get_url()):
            print(f"{Fore.GREEN}✅ 前端服务已启动: {frontend_config.get_url()}{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}❌ 前端服务启动失败{Style.RESET_ALL}")
            return False
    
    def _wait_for_service(self, service_name: str, url: str, timeout: int = 30) -> bool:
        """等待服务启动"""
        print(f"  等待 {service_name} 服务启动...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=2)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            
            time.sleep(1)
        
        return False
    
    def stop_services(self):
        """停止所有服务"""
        print(f"{Fore.CYAN}停止所有服务...{Style.RESET_ALL}")
        
        for service_name, process in self.processes.items():
            try:
                print(f"  停止 {service_name}...")
                process.terminate()
                
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                
                print(f"  {Fore.GREEN}✅ {service_name} 已停止{Style.RESET_ALL}")
                
            except Exception as e:
                print(f"  {Fore.RED}❌ 停止 {service_name} 失败: {e}{Style.RESET_ALL}")
        
        self.processes.clear()
    
    def show_environment_variables(self, service: str, environment: str = "development"):
        """显示环境变量"""
        print(f"{Fore.CYAN}{service} 服务环境变量 (环境: {environment}):{Style.RESET_ALL}\n")
        
        env_vars = services_config_manager.get_environment_variables(service, environment)
        
        if not env_vars:
            print("无环境变量配置")
            return
        
        env_table = []
        for key, value in env_vars.items():
            # 隐藏敏感信息
            if "key" in key.lower() or "password" in key.lower() or "secret" in key.lower():
                display_value = "***隐藏***"
            else:
                display_value = str(value)
            
            env_table.append([key, display_value])
        
        headers = ["变量名", "值"]
        print(tabulate(env_table, headers=headers, tablefmt="grid"))
    
    def export_config(self, output_file: str, environment: str = "development"):
        """导出服务配置"""
        print(f"{Fore.CYAN}导出服务配置到 {output_file}...{Style.RESET_ALL}")
        
        try:
            config_data = {
                "environment": environment,
                "backend": {
                    "config": get_backend_config(environment).__dict__,
                    "cors": services_config_manager.get_cors_config(environment),
                    "websocket": services_config_manager.get_websocket_config(environment),
                    "logging": services_config_manager.get_logging_config("backend", environment)
                },
                "frontend": {
                    "config": get_frontend_config(environment).__dict__,
                    "api_connection": get_api_connection_config(environment)
                },
                "health_checks": services_config_manager.get_health_check_config(environment)
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"{Fore.GREEN}✅ 配置已导出到: {output_file}{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ 导出配置失败: {e}{Style.RESET_ALL}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="服务管理CLI工具")
    parser.add_argument(
        "--env",
        choices=["development", "testing", "production"],
        default="development",
        help="运行环境"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 验证命令
    subparsers.add_parser("validate", help="验证服务配置")
    
    # 配置显示命令
    show_parser = subparsers.add_parser("show", help="显示服务配置")
    show_parser.add_argument("--service", choices=["backend", "frontend", "api"], help="指定服务")
    
    # 状态检查命令
    subparsers.add_parser("status", help="检查服务状态")
    
    # 启动命令
    start_parser = subparsers.add_parser("start", help="启动服务")
    start_parser.add_argument("service", choices=["backend", "frontend"], help="要启动的服务")
    
    # 停止命令
    subparsers.add_parser("stop", help="停止所有服务")
    
    # 环境变量命令
    env_parser = subparsers.add_parser("env", help="显示环境变量")
    env_parser.add_argument("service", choices=["backend", "frontend"], help="服务名称")
    
    # 导出命令
    export_parser = subparsers.add_parser("export", help="导出服务配置")
    export_parser.add_argument("output", help="输出文件")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = ServicesCLI()
    
    try:
        if args.command == "validate":
            success = cli.validate_services(args.env)
            sys.exit(0 if success else 1)
        
        elif args.command == "show":
            cli.show_config(getattr(args, 'service', None), args.env)
        
        elif args.command == "status":
            success = cli.check_services_status(args.env)
            sys.exit(0 if success else 1)
        
        elif args.command == "start":
            success = cli.start_service(args.service, args.env)
            if success:
                print(f"\n按 Ctrl+C 停止服务")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    cli.stop_services()
            sys.exit(0 if success else 1)
        
        elif args.command == "stop":
            cli.stop_services()
        
        elif args.command == "env":
            cli.show_environment_variables(args.service, args.env)
        
        elif args.command == "export":
            cli.export_config(args.output, args.env)
    
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}操作已取消{Style.RESET_ALL}")
        cli.stop_services()
    except Exception as e:
        print(f"{Fore.RED}错误: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
